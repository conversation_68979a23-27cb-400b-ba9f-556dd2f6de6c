<?php

namespace app\admin\model\exercise;

use think\Model;

class Record extends Model
{
    // 表名
    protected $name = 'esop_exercise_records';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'approval_status_text',
        'stock_price_type_text'
    ];
    
    public function getApprovalStatusList()
    {
        return ['0' => __('Pending'), '1' => __('Approved'), '2' => __('Rejected')];
    }
    
    public function getStockPriceTypeList()
    {
        return ['1' => __('Average price'), '2' => __('Fixed price')];
    }
    
    public function getApprovalStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['approval_status'];
        $list = $this->getApprovalStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    public function getStockPriceTypeTextAttr($value, $data)
    {
        $value = $value ? $value : $data['stock_price_type'];
        $list = $this->getStockPriceTypeList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    public function user()
    {
        return $this->belongsTo('app\common\model\EsopUser', 'user_id', 'id')->setEagerlyType(0);
    }

    public function userProfile()
    {
        return $this->belongsTo('app\common\model\EsopUserProfile', 'user_id', 'user_id')->setEagerlyType(0);
    }

    public function stock()
    {
        return $this->belongsTo('app\common\model\EsopStockManagement', 'stock_id', 'id')->setEagerlyType(0);
    }
} 