<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopBAccount as EsopBAccountModel;
use app\common\model\EsopUser as EsopUserModel;
use app\common\model\EsopVestingRule as EsopVestingRuleModel;
use app\common\model\EsopGrantOperationLog as EsopGrantOperationLogModel;
use app\common\model\EsopInvitationRelation as EsopInvitationRelationModel;
use app\common\model\EsopUserAsset as EsopUserAssetModel;
use app\common\model\EsopBAccountAssetLog as EsopBAccountAssetLogModel;
use app\common\model\EsopUserFundChangeRecords; // 添加资金变化记录模型
use app\common\model\EsopGrantStatistics as EsopGrantStatisticsModel; // 添加授权统计模型
use think\Db;
use think\Exception;
use think\Validate;
use think\Queue;

/**
 * B端用户接口
 */
class Buser extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 添加授权
     * 
     * @ApiTitle    (添加授权)
     * @ApiSummary  (B端用户给目标用户授予期权)
     * @ApiMethod   (POST)
     * @ApiParams   (name="grant_amount", type="number", required=true, description="授予数量")
     * @ApiParams   (name="vesting_rule_id", type="integer", required=true, description="解禁规则ID")
     * @ApiParams   (name="exercise_fee", type="number", required=true, description="行权手续费")
     * @ApiParams   (name="target_user_id", type="integer", required=true, description="目标用户ID")
     * @ApiReturn   ({"code":1,"msg":"授权成功","time":"**********","data":{}})
     */
    public function addAuth()
    {
        // 验证是否为B端用户
        $userId = $this->auth->id;
        $bAccount = EsopBAccountModel::whereRaw("FIND_IN_SET({$userId}, user_ids)")
            ->find();
        
        if (!$bAccount) {
            return $this->toError('只有B端用户可以进行授权操作');
        }
        
        // 获取并验证参数
        $params = $this->request->post();
        $validate = new Validate([
            'grant_amount'    => 'require|float|gt:0',
            'vesting_rule_id' => 'require|integer|gt:0',
            'exercise_fee'    => 'require|float|egt:0',
            'target_user_id'  => 'require|integer|gt:0'
        ]);
        
        if (!$validate->check($params)) {
            return $this->toError($validate->getError());
        }
        
        // 检查目标用户是否存在
        $targetUser = EsopUserModel::where('status', 1)->where('id', $params['target_user_id'])->find();
        if (!$targetUser) {
            return $this->toError('目标用户不存在');
        }
        
        // 查询是否为团队下的C端用户
        $teamRelation = EsopInvitationRelationModel::whereRaw("FIND_IN_SET({$userId}, root_id)")
            ->where('invitee_id', $params['target_user_id'])
            ->count();

        // 如果既不是团队下的C端用户则不允许授权
        if (empty($teamRelation)) {
            return $this->toError('只能授权给您团队下的C端用户');
        }
        
        
        // 检查目标用户必须是审核通过的正常用户
        $profileStatus = EsopUserModel::checkVerificationStatus($params['target_user_id']);
        if (!$profileStatus['is_verified']) {
            return $this->toError('只能授权给已完成实名认证的用户：' . $profileStatus['status_text']);
        }
        
        // 检查解禁规则是否存在
        $vestingRule = EsopVestingRuleModel::get($params['vesting_rule_id']);
        if (!$vestingRule) {
            return $this->toError('解禁规则不存在');
        }
        
            
        // 检查可用余额是否足够，使用bccomp进行高精度比较，避免浮点数精度问题
        // bccomp返回-1表示左边小于右边，0表示相等，1表示左边大于右边
        if (bccomp((string)$bAccount->available_assets, (string)$params['grant_amount'], 8) < 0) {
            // 可用余额不足，无法进行授权
            return $this->toError('可用余额不足，无法进行授权');
        }
        
        Db::startTrans();
        try {
            // 计算B端账户资产变化前后的值
            $beforeAvailable = $bAccount->available_assets;
            $afterAvailable = bcsub($bAccount->available_assets, $params['grant_amount'], 8);
            
            // 获取目标用户授权前资产数据
            $targetUserAsset = EsopUserAssetModel::where('user_id', $params['target_user_id'])->find();
            $beforeUserAsset = [];
            
            if (!$targetUserAsset) {
                // 如果目标用户没有资产记录，创建初始资产数据
                $beforeUserAsset = [
                    'available_assets' => 0,      
                    'pending_assets' => 0,
                    'locked_assets' => 0, // 添加locked_assets字段初始值
                ];
            } else {
                $beforeUserAsset = $targetUserAsset->toArray();
                // 确保locked_assets字段存在
                if (!isset($beforeUserAsset['locked_assets'])) {
                    $beforeUserAsset['locked_assets'] = 0;
                }
            }
            
            // 计算授权后目标用户的资产数据
            $afterUserAsset = [
                'available_assets' => $beforeUserAsset['available_assets'],
                'pending_assets' => bcadd($beforeUserAsset['pending_assets'], $params['grant_amount'], 8),
                'locked_assets' => $beforeUserAsset['locked_assets'] // 添加locked_assets字段
            ];

            // 获取最后一条授权记录
            $batchNum = EsopGrantOperationLogModel::where('target_user_id', $params['target_user_id'])
                ->where('b_account_id', $bAccount->id)
                ->where('b_account_alias', $bAccount->alias)
                ->count();
            
            // 记录授权日志（添加了新字段记录授权前后的资产变化情况）
            $logData = [
                'user_id'         => $userId,
                'target_user_id'  => $params['target_user_id'],
                'grant_amount'    => $params['grant_amount'],
                'vesting_rule_id' => $params['vesting_rule_id'],
                'exercise_fee'    => $params['exercise_fee'],
                'created_at'      => date('Y-m-d H:i:s'),
                'batch_no' => $bAccount->alias.date('Y').'第'.($batchNum+1).'批次',
                'b_account_id' => $bAccount->id,
                'b_account_alias' => $bAccount->alias,
            ];
            
            $log = new EsopGrantOperationLogModel();
            $log->save($logData);
            
            // 更新B端账户资产
            // 使用模型的updateAccountAsset方法，支持乐观锁和事务
            \app\common\model\EsopBAccount::updateAccountAsset($bAccount->id, [
                'available_assets' => '-' . $params['grant_amount'], // 减少可用资产
                'invested_assets' => $params['grant_amount']  // 增加已投资资产
            ]);
            
            // 记录B端账户资产变化日志
            EsopBAccountAssetLogModel::recordAssetChange(
                $bAccount->id, 
                2, // 变更类型：2-减少（授权操作减少可用资产）
                $params['grant_amount'], 
                $beforeAvailable, 
                $afterAvailable,
                '授权给用户ID：' . $params['target_user_id'],
                $userId
            );
            
            // 更新目标用户资产
            $result = EsopUserAssetModel::changeUserAsset($params['target_user_id'], [
                'pending_assets' => $params['grant_amount']  // 初始全部为待解冻资产
            ]);
            
            // 记录用户资金变化
            EsopUserFundChangeRecords::addRecord(
                $params['target_user_id'],
                $params['grant_amount'],
                EsopUserFundChangeRecords::ACTION_TYPE_AUTHORIZE, // 授权
                $beforeUserAsset,
                $afterUserAsset,
                '收到授权' . $params['grant_amount'] . '，来自B端账户ID：' . $bAccount->id,
                ['b_account_id' => $bAccount->id, 'b_account_alias' => $bAccount->alias,'b_account_name' => $bAccount->account_name]
            );
            
            // 获取操作人信息（当前登录用户）
            $operatorProfile = \app\common\model\EsopUserProfile::where('user_id', $userId)->find();
            $operatorName = $operatorProfile ? $operatorProfile->real_name : '';
            
            // 获取目标用户信息（被授权用户）
            $targetUserProfile = \app\common\model\EsopUserProfile::where('user_id', $params['target_user_id'])->find();
            $targetUserName = $targetUserProfile ? $targetUserProfile->real_name : '';
            
            // 准备授权统计数据
            $statisticsDataList = [
                [
                    // B端账户资金减少记录
                    'account_id' => $bAccount->id,
                    'account_name' => $bAccount->account_name,
                    'account_type' => 1, // 1-B端账户
                    'grant_amount' => '-' . $params['grant_amount'], // 负数表示减少
                    'operator_id' => $userId,
                    'operator_name' => $operatorName,
                    'after_available_assets' => $afterAvailable,
                    'after_pending_assets' => 0, // B端账户没有待解冻资产概念
                    'remark' => 'B端账户授权给用户：' . $targetUserName,
                    'created_at' => date('Y-m-d H:i:s')
                ],
                [
                    // C端用户资金增加记录
                    'account_id' => $params['target_user_id'],
                    'account_name' => $targetUserName,
                    'account_type' => 2, // 2-C端账户
                    'grant_amount' => $params['grant_amount'],
                    'operator_id' => $userId,
                    'operator_name' => $operatorName,
                    'after_available_assets' => $afterUserAsset['available_assets'],
                    'after_pending_assets' => $afterUserAsset['pending_assets'],
                    'remark' => '收到来自B端账户（' . $bAccount->account_name . '）的授权',
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ];
            
            // 批量保存授权统计记录
            EsopGrantStatisticsModel::addBatchRecords($statisticsDataList);

            // 记录资产变化日志
            \app\common\model\EsopAssetChangeLog::batchRecordAssetChange(
                [
                    [
                        'user_id' => $params['target_user_id'],
                        'b_account_id' => $bAccount->id,
                        'stock_id' => 0,
                        'amount' => $params['grant_amount'],
                        'action_name' => '授权',
                        'extra_name' => $bAccount->account_name,
                        'sub_name' => '待解禁资产',
                        'is_audit' => 0,
                        'audit_status' => 0,
                        'extends' => ['user_id' => $userId, 'target_user_id' => $params['target_user_id'],'grant_id' => $log->id,'b_account_id' => $bAccount->id],
                        'relation_id' => $log->id
                    ]
                ]
            );
            
            // 将生成解禁记录的任务加入队列
            $jobData = [
                'rule_id' => $params['vesting_rule_id'],
                'grant_id' => $log->id,
            ];

            // 添加到队列中，异步处理解禁记录生成
            Queue::push('app\common\job\GenerateVestingRecords', $jobData, 'vesting');
            
            Db::commit();
            return $this->jsonSucess('授权成功', ['log_id' => $log->id]);
        } catch (Exception $e) {
            Db::rollback();
            return $this->toError('授权失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取B端管理员用户团队成员列表
     * 
     * @ApiTitle    (获取团队成员列表)
     * @ApiSummary  (获取B端管理员的团队成员列表)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiParams   (name="keyword", type="string", required=false, description="搜索关键词，支持姓名和手机号搜索")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"**********","data":{"total":100,"rows":[{"id":1,"user_id":1,"real_name":"张三","phone":"13800138000","gender":1,"invitation_time":"2023-01-01 12:00:00"}]}})
     */
    public function teamMemberList()
    {
        $userId = $this->auth->id;
        
        // 获取分页参数
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $keyword = $this->request->get('keyword', '');
        
        // 初始化返回数据结构
        $result = [
            'total' => 0,
            'rows' => []
        ];
        
        // 验证是否为B端用户
        $bAccount = EsopBAccountModel::whereRaw("FIND_IN_SET({$userId}, user_ids)")
            ->find();
            
        if (!$bAccount) {
            // 不是B端管理员，返回空列表
            return $this->jsonSucess('获取成功', $result);
        }
        
        try {
            // 查询团队成员
            $query = Db::name('esop_invitation_relations')
                ->alias('r')
                ->join('esop_users u', 'r.invitee_id = u.id')
                ->join('esop_user_profiles p', 'u.id = p.user_id', 'left')
                ->whereRaw("FIND_IN_SET({$userId}, r.root_id)")
                ->where('u.status', 1)
                ->where('p.audit_status', 1);

            // 添加关键词搜索
            if (!empty($keyword)) {
                $query->where(function ($query) use ($keyword) {
                    $query->whereOr('p.real_name', 'like', "%{$keyword}%")
                          ->whereOr('u.phone', 'like', "%{$keyword}%");
                });
            }
                
            $members = $query->field([
                'u.id as user_id',
                'p.real_name',
                'u.phone',
                'p.gender',
                'r.created_at as register_time',
                'p.avatar'
            ])
            ->paginate($limit, false, ['page' => $page]);
            
            $rows = $members->items();
            
            // 补充性别显示文本
            foreach ($rows as &$member) {
                switch ($member['gender']) {
                    case 1:
                        $member['gender_text'] = '男';
                        break;
                    case 2:
                        $member['gender_text'] = '女';
                        break;
                    default:
                        $member['gender_text'] = '保密';
                }
            }
            
            $result['total'] = $members->total();
            $result['rows'] = $rows;
            
            return $this->jsonSucess('获取成功', $result);
        } catch (Exception $e) {
            return $this->toError('获取团队成员列表失败: ' . $e->getMessage());
        }
    }
}
