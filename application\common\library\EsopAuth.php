<?php

namespace app\common\library;

use app\common\model\EsopUser;
use think\Hook;

/**
 * ESOP 专用认证类
 * 继承 FastAdmin 原生 Auth，核心差异是用户模型改为 EsopUser
 */
class EsopAuth extends Auth
{
    /**
     * 根据 Token 初始化登录状态（重写父类方法）
     *
     * @param string $token 客户端传递的 Token
     * @return bool 是否初始化成功
     */
    public function init($token)
    {
        // 已登录直接返回
        if ($this->_logined) {
            return true;
        }
        // 若之前已有错误信息，则直接返回失败
        if ($this->_error) {
            return false;
        }

        // 读取 Token 信息
        $data = Token::get($token);
        if (!$data) {
            return false; // Token 无效
        }

        $userId = intval($data['user_id']);
        if ($userId <= 0) {
            $this->setError('未登录');
            return false;
        }

        // 关键点：改为查询 esop_users 表
        $user = EsopUser::get($userId);
        if (!$user) {
            $this->setError('账号不存在');
            return false;
        }

        // ESOP 用户状态：1 表示正常
        if ($user['status'] != 1) {
            $this->setError('账号已被禁用');
            return false;
        }

        // 设置登陆态
        $this->_user    = $user;
        $this->_logined = true;
        $this->_token   = $token;

        // 触发与父类保持一致的事件，方便后续监听
        Hook::listen('user_init_successed', $this->_user);

        return true;
    }
} 