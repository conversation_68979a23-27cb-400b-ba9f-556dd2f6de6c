<?php

namespace app\common\model;

use think\Model;

/**
 * ESOP用户资料模型
 */
class EsopUserProfile extends Model
{
    // 表名
    protected $name = 'esop_user_profiles';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [
        'gender_text',
        'marital_status_text',
        'audit_status_text'
    ];

    // 性别列表
    public static $genderList = [
        0 => '保密',
        1 => '男',
        2 => '女'
    ];

    // 婚姻状态列表
    protected static $maritalStatusList = [
        0 => '保密',
        1 => '未婚',
        2 => '已婚',
        3 => '离异',
        4 => '丧偶'
    ];

    // 审核状态列表
    protected static $auditStatusList = [
        0 => '待审核',
        1 => '审核通过',
        2 => '审核不通过'
    ];

    /**
     * 获取性别文本
     */
    public function getGenderTextAttr($value, $data)
    {
        return isset($data['gender']) ? self::$genderList[$data['gender']] : '';
    }

    /**
     * 获取婚姻状态文本
     */
    public function getMaritalStatusTextAttr($value, $data)
    {
        return isset($data['marital_status']) ? self::$maritalStatusList[$data['marital_status']] : '';
    }

    /**
     * 获取审核状态文本
     */
    public function getAuditStatusTextAttr($value, $data)
    {
        return isset($data['audit_status']) ? self::$auditStatusList[$data['audit_status']] : '';
    }

    /**
     * 获取器: 将birth_date转为Y-m-d格式
     */
    public function getBirthDateAttr($value)
    {
        return $value ? date('Y-m-d', strtotime($value)) : '';
    }

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }

    /**
     * 关联身份证图片模型
     */
    public function idCardImages()
    {
        return $this->hasOne('EsopIdCardImages', 'user_id', 'user_id');
    }

    /**
     * 根据用户ID获取资料
     * 
     * @param int $userId 用户ID
     * @return EsopUserProfile|null
     */
    public static function getByUserId($userId)
    {
        return self::where('user_id', $userId)
            ->find();
    }

    /**
     * 创建或更新用户资料
     * 
     * @param int $userId 用户ID
     * @param array $data 资料数据
     * @return EsopUserProfile
     */
    public static function createOrUpdate($userId, $data)
    {
        $profile = self::getByUserId($userId);

        if (!$profile) {
            // 创建新资料
            $profile = new self();
            $profile->user_id = $userId;
            
            // 如果创建新资料且没有提供nickname，设置默认昵称
            if (!isset($data['nickname']) || empty($data['nickname'])) {
                $user = \app\common\model\EsopUser::get($userId);
                if ($user) {
                    // 如果有真实姓名，使用真实姓名作为昵称
                    if (isset($data['real_name']) && !empty($data['real_name'])) {
                        $data['nickname'] = $data['real_name'];
                    } 
                    // 否则使用脱敏手机号作为昵称
                    else if ($user->phone) {
                        $data['nickname'] = substr($user->phone, 0, 3) . '****' . substr($user->phone, -4);
                    } 
                    // 如果都没有，使用用户ID作为昵称
                    else {
                        $data['nickname'] = '用户' . $userId;
                    }
                } else {
                    $data['nickname'] = '用户' . $userId;
                }
            }
            
            // 如果创建新资料且没有提供avatar，设置默认头像
            if (!isset($data['avatar']) || empty($data['avatar'])) {
                $data['avatar'] = '/assets/img/avatar.png';
            }
        }

        // 更新资料字段
        if (isset($data['real_name'])) $profile->real_name = $data['real_name'];
        if (isset($data['gender'])) $profile->gender = $data['gender'];
        if (isset($data['birth_date'])) $profile->birth_date = $data['birth_date'];
        if (isset($data['id_card_no'])) $profile->id_card_no = $data['id_card_no'];
        if (isset($data['marital_status'])) $profile->marital_status = $data['marital_status'];
        if (isset($data['securities_account'])) $profile->securities_account = $data['securities_account'];
        if (isset($data['nickname'])) $profile->nickname = $data['nickname'];
        if (isset($data['avatar'])) $profile->avatar = $data['avatar'];
        if (isset($data['audit_status'])) $profile->audit_status = $data['audit_status'];
        if (isset($data['audit_remark'])) $profile->audit_remark = $data['audit_remark'];
        if (isset($data['audit_time'])) $profile->audit_time = $data['audit_time'];
        if (isset($data['audit_user_id'])) $profile->audit_user_id = $data['audit_user_id'];
        if (isset($data['trade_password'])) $profile->trade_password = password_hash($data['trade_password'], PASSWORD_DEFAULT);
        
        // 添加银行卡相关字段处理
        if (isset($data['bank_name'])) $profile->bank_name = $data['bank_name'];
        if (isset($data['bank_branch'])) $profile->bank_branch = $data['bank_branch'];
        if (isset($data['bank_account'])) $profile->bank_account = $data['bank_account'];
        if (isset($data['bank_image'])) $profile->bank_image = $data['bank_image'];
        
        $profile->save();

        // 处理身份证图片
        if (isset($data['front_image']) || isset($data['back_image'])) {
            self::saveIdCardImages($userId, $data);
        }
        
        return $profile;
    }
    
    /**
     * 保存身份证图片
     * 
     * @param int $userId 用户ID
     * @param array $data 图片数据
     * @return EsopIdCardImages|bool
     */
    public static function saveIdCardImages($userId, $data)
    {
        $model = new \app\common\model\EsopIdCardImages();
        $images = $model->where('user_id', $userId)->find();
        
        if (!$images) {
            $images = new \app\common\model\EsopIdCardImages();
            $images->user_id = $userId;
            $images->created_at = date('Y-m-d H:i:s');
        }
        
        if (isset($data['front_image'])) {
            $images->front_image = $data['front_image'];
        }
        
        if (isset($data['back_image'])) {
            $images->back_image = $data['back_image'];
        }
        
        $images->upload_time = date('Y-m-d H:i:s');
        $images->is_verified = 0; // 默认为未验证状态
        $images->updated_at = date('Y-m-d H:i:s');
        
        return $images->save();
    }
    
    /**
     * 获取用户的身份证图片
     * 
     * @param int $userId 用户ID
     * @return array 包含前后两张图片的数组
     */
    public static function getIdCardImages($userId)
    {
        $model = new \app\common\model\EsopIdCardImages();
        $images = $model->where('user_id', $userId)->find();
        
        if (!$images) {
            return [
                'front_image' => '',
                'back_image' => '',
                'is_verified' => 0
            ];
        }
        
        return [
            'front_image' => $images->front_image,
            'back_image' => $images->back_image,
            'is_verified' => $images->is_verified
        ];
    }

    /**
     * 软删除用户资料
     * 
     * @param int $userId 用户ID
     * @return boolean
     */
    public static function softDeleteByUserId($userId)
    {
        $profile = self::getByUserId($userId);
        if (!$profile) {
            return false;
        }
        $profile->delete();
        
        // 同时软删除身份证图片
        $model = new \app\common\model\EsopIdCardImages();
        $images = $model->where('user_id', $userId)->find();
        if ($images) {
            $images->delete();
        }
        
        return true;
    }

    /**
     * 设置交易密码
     * 
     * @param int $userId 用户ID
     * @param string $tradePassword 交易密码(明文)
     * @return bool 是否设置成功
     */
    public static function setTradePassword($userId, $tradePassword)
    {
        if (!$userId || !$tradePassword) {
            return false;
        }
        
        $profile = self::getByUserId($userId);
        if (!$profile) {
            $profile = new self();
            $profile->user_id = $userId;
            $profile->created_at = date('Y-m-d H:i:s');
        }
        
        $profile->trade_password = password_hash($tradePassword, PASSWORD_DEFAULT);
        $profile->updated_at = date('Y-m-d H:i:s');
        
        return $profile->save();
    }
    
    /**
     * 验证交易密码
     * 
     * @param int $userId 用户ID
     * @param string $tradePassword 交易密码(明文)
     * @return bool 密码是否正确
     */
    public static function verifyTradePassword($userId, $tradePassword)
    {
        $profile = self::getByUserId($userId);
        
        if (!$profile || empty($profile->trade_password)) {
            return false;
        }
        
        return password_verify($tradePassword, $profile->trade_password);
    }
    
    /**
     * 检查用户是否已设置交易密码
     * 
     * @param int $userId 用户ID
     * @return bool 是否已设置
     */
    public static function hasTradePassword($userId)
    {
        $profile = self::getByUserId($userId);
        return $profile && !empty($profile->trade_password);
    }
}
