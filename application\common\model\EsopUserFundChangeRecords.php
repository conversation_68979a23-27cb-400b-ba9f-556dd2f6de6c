<?php

namespace app\common\model;

use think\Model;

/**
 * 用户资金变化记录模型
 */
class EsopUserFundChangeRecords extends Model
{
    // 表名
    protected $name = 'esop_user_fund_change_records';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    const ACTION_TYPE_EXCHANGE = 1; // 换股
    const ACTION_TYPE_UNLOCK = 2; // 解禁
    const ACTION_TYPE_TRANSFER = 3; // 转出
    const ACTION_TYPE_ACCEPT = 4; // 接受
    const ACTION_TYPE_AUTHORIZE = 5; // 授权
    const ACTION_TYPE_LOCK = 6; // 锁定资金
    const ACTION_TYPE_UNLOCK_FUND = 7; // 解锁资金

    public static function getActionTypeText($actionType)
    {
        switch ($actionType) {
            case self::ACTION_TYPE_EXCHANGE:
                return '换股';
            case self::ACTION_TYPE_UNLOCK:
                return '解禁';
            case self::ACTION_TYPE_TRANSFER:
                return '转出';
            case self::ACTION_TYPE_ACCEPT:
                return '接受';  
            case self::ACTION_TYPE_AUTHORIZE:
                return '授权';
            case self::ACTION_TYPE_LOCK:
                return '锁定资金';
            case self::ACTION_TYPE_UNLOCK_FUND:
                return '解锁资金';  
        }
    }
    
    /**
     * 添加资金变化记录
     * 
     * @param int $userId 用户ID
     * @param float $amount 金额
     * @param int $actionType 操作类型：1-换股，2-解禁，3-转出，4-接受，5-授权，6-锁定资金,7-解锁资金
     * @param array $beforeAsset 操作前资产
     * @param array $afterAsset 操作后资产
     * @param string $remark 备注
     * @param array $extends 扩展字段
     * @return int 新记录ID
     */
    public static function addRecord($userId, $amount, $actionType, $beforeAsset, $afterAsset, $remark = '',$extends=null)
    {
        $data = [
            'user_id' => $userId,
            'amount' => $amount,
            'action_type' => $actionType,
            'before_available_assets' => $beforeAsset['available_assets'],
            'after_available_assets' => $afterAsset['available_assets'],
            'before_pending_assets' => $beforeAsset['pending_assets'],
            'after_pending_assets' => $afterAsset['pending_assets'],
            'before_locked_assets' => $beforeAsset['locked_assets'],
            'after_locked_assets' => $afterAsset['locked_assets'],    
            'remark' => $remark,
            'extends' => json_encode($extends),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $model = new self;
        $model->data($data);
        $model->save();
        
        return $model->id;
    }

    /**
     * 批量添加资金变更记录
     * 
     * @param array $recordsData 资金变更记录数据数组，每条记录包含：
     *        - user_id: 用户ID
     *        - amount: 变更金额
     *        - action_type: 变更类型
     *        - before_asset: 变更前资产数据
     *        - after_asset: 变更后资产数据
     *        - remark: 备注
     * @return boolean 是否添加成功
     */
    public static function batchAddRecords($recordsData)
    {
        if (empty($recordsData)) {
            return false;
        }
        
        $records = [];
        $now = time();
        
        foreach ($recordsData as $data) {
            $records[] = [
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'action_type' => $data['action_type'],
                'before_available_assets' => $data['before_asset']['available_assets'],
                'after_available_assets' => $data['after_asset']['available_assets'],
                'before_pending_assets' => $data['before_asset']['pending_assets'],
                'after_pending_assets' => $data['after_asset']['pending_assets'],
                'before_locked_assets' => $data['before_asset']['locked_assets'],
                'after_locked_assets' => $data['after_asset']['locked_assets'],
                'remark' => $data['remark'],
                'extends' => json_encode($data['extends']),
                'created_at' => date('Y-m-d H:i:s')
            ];
        }
        
        // 批量插入记录
        return (new self())->saveAll($records);
    }
} 