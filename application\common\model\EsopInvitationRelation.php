<?php

namespace app\common\model;

use think\Model;

/**
 * 邀请关系模型
 * 对应表: esop_invitation_relations
 */
class EsopInvitationRelation extends Model
{
    // 表名
    protected $name = 'esop_invitation_relations';

    // 自动时间戳
    protected $autoWriteTimestamp = false;

    // 时间戳字段
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 创建邀请关系
     * 
     * @param int $inviter_id 邀请人ID
     * @param int $invitee_id 被邀请人ID
     * @param int $b_account_id 邀请人B端账号ID
     * @param string $root_id 根ID
     * @param string $invitation_code 邀请码
     * @return bool 是否创建成功
     */
    public static function createRelation($inviter_id, $invitee_id, $b_account_id=null, $root_id=null, $invitation_code=null)
    {
        if (!$inviter_id || !$invitee_id) {
            return false;
        }

        // 不能自己邀请自己
        if ($inviter_id == $invitee_id) {
            return false;
        }
        
        // 先检查是否已存在邀请关系
        $exists = self::where('inviter_id', $inviter_id)
                     ->where('invitee_id', $invitee_id)
                     ->find();
                     
        // 如果已存在关系则不需要重复创建
        if ($exists) {
            return true;
        }
        
        $model = new self();
        $model->inviter_id = $inviter_id;
        $model->invitee_id = $invitee_id;
        $model->b_account_id = $b_account_id;
        $model->root_id = $root_id;
        $model->invitation_code = $invitation_code;
        $model->created_at = date('Y-m-d H:i:s');
        $model->updated_at = date('Y-m-d H:i:s');
        
        return $model->save();
    }
} 