<?php

namespace app\common\model;

use think\Model;

/**
 * 邀请码模型
 */
class EsopInvitationCode extends Model
{
    // 表名
    protected $name = 'esop_invitation_codes';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [
        'status_text'
    ];
    
    /**
     * 获取邀请码状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '未使用',
            1 => '已使用',
        ];
        return isset($status[$data['is_used']]) ? $status[$data['is_used']] : '';
    }

    /**
     * 批量生成邀请码
     * 
     * @param int $count 生成数量
     * @param string $remark 备注
     * @param int $user_id 用户ID，null或0表示管理端生成，非null表示用户生成
     * @return array 生成的邀请码数组
     */
    public static function generateCodes($count, $remark = '', $user_id = null)
    {
        $codes = [];
        for ($i = 0; $i < $count; $i++) {
            $code = self::generateUniqueCode();
            $model = new self();
            $model->code = $code;
            $model->is_used = 0;
            $model->user_id = $user_id;
            $model->remark = $remark;
            $model->save();
            $codes[] = $code;
        }
        return $codes;
    }

    /**
     * 生成唯一邀请码
     * 
     * @return string 邀请码
     */
    protected static function generateUniqueCode()
    {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $length = 8;
        
        do {
            $code = '';
            for ($i = 0; $i < $length; $i++) {
                $code .= $characters[mt_rand(0, strlen($characters) - 1)];
            }
            // 检查邀请码是否已存在
            $exists = self::where('code', $code)->find();
        } while ($exists);
        
        return $code;
    }

    /**
     * 使用邀请码
     * 
     * @param string $code 邀请码
     * @return bool 是否使用成功
     */
    public static function useCode($code)
    {
        $invitationCode = self::where('code', $code)
            ->where('is_used', 0)
            ->find();
            
        if (!$invitationCode) {
            return false;
        }
        
        $invitationCode->is_used = 1;
        return $invitationCode->save();
    }
} 