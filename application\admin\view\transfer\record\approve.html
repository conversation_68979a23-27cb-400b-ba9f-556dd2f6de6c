<form id="approve-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="c-transfer-id" class="control-label col-xs-12 col-sm-3">{:__('Transfer ID')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-transfer-id" value="{$row.id}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-from-user" class="control-label col-xs-12 col-sm-3">{:__('From user')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-from-user" value="{$row.user_profile.real_name} {$row.user.phone|default=''}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-to-user" class="control-label col-xs-12 col-sm-3">{:__('To user')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-to-user" value="{$row.target_user_profile.real_name} {$row.target_user.phone|default=''}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-amount" class="control-label col-xs-12 col-sm-3">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-amount" value="{$row.amount}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-remark" class="control-label col-xs-12 col-sm-3">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-remark" value="{$row.remark}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="approvalStatusList" item="vo" key="k"}
                {if in_array($k, [1,2])} 
                <label for="row[approval_status]-{$k}"><input id="row[approval_status]-{$k}" name="row[approval_status]" type="radio" value="{$k}" {in name="k" value="1"}checked{/in} /> {$vo}</label>
                {/if}
                {/foreach}
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-approval-remark" class="control-label col-xs-12 col-sm-3">{:__('Approval remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-approval-remark" name="row[approval_remark]" class="form-control" rows="5"></textarea>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 