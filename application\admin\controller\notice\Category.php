<?php

namespace app\admin\controller\notice;

use app\common\controller\Backend;
use app\common\model\EsopArticleCategory;

/**
 * 公告中心分类管理
 * @icon fa fa-folder-open
 */
class Category extends Backend
{
    protected $model = null;
    protected $sort = 'id';
    protected $order = 'DESC';
    protected $boardId = 1; // 公告中心

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopArticleCategory();
    }

    public function index()
    {
        $this->request->filter(['strip_tags','trim']);
        if($this->request->isAjax()){
            if($this->request->request('keyField')){return $this->selectpage();}
            list($where,$sort,$order,$offset,$limit)=$this->buildparams();
            $list = $this->model->where($where)->where('board_id',$this->boardId)->order($sort,$order)->paginate($limit);
            return json(['total'=>$list->total(),'rows'=>$list->items()]);
        }
        return $this->view->fetch();
    }

    public function add()
    {
        if($this->request->isPost()){
            $params=$this->request->post('row/a',[],'trim');
            $params['board_id']=$this->boardId;
            $this->request->post(['row'=>$params]);
        }
        return parent::add();
    }
} 