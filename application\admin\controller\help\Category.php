<?php

namespace app\admin\controller\help;

use app\common\controller\Backend;
use app\common\model\EsopArticleCategory;

/**
 * 帮助中心分类管理
 * @icon fa fa-folder
 */
class Category extends Backend
{
    protected $model = null;
    protected $sort = 'id';
    protected $order = 'DESC';

    // 固定板块 0
    protected $boardId = 0;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopArticleCategory();
    }

    /**
     * 查看
     */
    public function index()
    {
        $this->request->filter(['strip_tags','trim']);
        if($this->request->isAjax()){
            if($this->request->request('keyField')){return $this->selectpage();}
            list($where,$sort,$order,$offset,$limit)=$this->buildparams();
            // 强制板块条件链式追加，避免修改$where结构
            $list = $this->model
                ->where($where)
                ->where('board_id','=',$this->boardId)
                ->order($sort,$order)
                ->paginate($limit);
            $result=["total"=>$list->total(),"rows"=>$list->items()];
            return json($result);
        }
        return $this->view->fetch();
    }

    /** 添加前填充 board_id */
    public function add()
    {
        if($this->request->isPost()){
            $params=$this->request->post('row/a',[],'strip_tags');
            $params['board_id']=$this->boardId;
            $this->request->post(['row'=>$params]);
        }
        return parent::add();
    }
} 