define(['jquery','bootstrap','backend','table','form'],function($,undefined,Backend,Table,Form){
    var Controller={
        index:function(){
            Table.api.init({extend:{
                index_url:'notice/article/index'+location.search,
                add_url:'notice/article/add',
                edit_url:'notice/article/edit',
                del_url:'notice/article/del',
                multi_url:'notice/article/multi',
                approve_url:'notice/article/approve',
                table:'esop_articles'
            }});
            var table=$("#table");
            table.bootstrapTable({
                url:$.fn.bootstrapTable.defaults.extend.index_url,
                pk:'id',
                sortName:'id',
                columns:[[{
                    checkbox:true
                },{
                    field:'id',title:'ID',sortable:true
                },{
                    field:'title',title:'标题',operate:'LIKE'
                },{
                    field:'category_name',title:'分类',operate:'LIKE'
                },{
                    field:'b_account_name',title:'B端',operate:false
                },{
                    field:'is_top_text',title:'置顶',operate:false
                },{
                    field:'approval_status_text',title:'审批状态',searchList:{"待审批":"待审批","通过":"通过","驳回":"驳回"},formatter:function(value){
                        var colorMap = { '待审批':'info','通过':'success','驳回':'danger' };
                        return '<span class="label label-'+ (colorMap[value]||'default') +'">'+ value +'</span>';
                    },operate:false
                },{
                    field:'approval_status',title:'状态值',visible:false,operate:false
                },{
                    field:'created_at',title:'创建时间',operate:'RANGE',addclass:'datetimerange',formatter:Table.api.formatter.datetime
                },{
                    field:'operate',title:'操作',table:table,events:Table.api.events.operate,formatter:Table.api.formatter.operate,
                    buttons:[
                        {
                            name:'approve',
                            title:'审批',
                            classname:'btn btn-xs btn-success btn-dialog',
                            icon:'fa fa-check-circle',
                            url:'notice/article/approve',
                            text:'审批',
                            visible:function(row){ return row.approval_status == 0; }
                        }
                    ]
                }]]
            });
            Table.api.bindevent(table);
        },
        add:function(){Controller.api.bindevent();},
        edit:function(){Controller.api.bindevent();},
        approve:function(){Controller.api.bindevent();},
        api:{
            bindevent:function(){Form.api.bindevent($('form[role=form]'));}
        }
    };return Controller;
}); 