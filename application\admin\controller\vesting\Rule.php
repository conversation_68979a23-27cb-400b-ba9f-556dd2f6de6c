<?php

namespace app\admin\controller\vesting;

use app\common\controller\Backend;
use app\common\model\EsopVestingRule;

/**
 * 解禁规则管理
 *
 * @icon fa fa-calendar-check-o
 */
class Rule extends Backend
{
    /**
     * EsopVestingRule 模型对象
     * @var \app\common\model\EsopVestingRule
     */
    protected $model = null;

    /** 默认排序字段 */
    protected $sort  = 'id';
    protected $order = 'DESC';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopVestingRule();

        // 枚举列表供模板使用
        $this->view->assign('ruleTypeList',       EsopVestingRule::$ruleTypeList);
        $this->view->assign('executionTypeList',  EsopVestingRule::$executionTypeList);
        $this->view->assign('statusList',         EsopVestingRule::$statusList);
        $this->view->assign('isDecreaseList',     [0=>'否',1=>'是']);
    }

    /**
     * 查看
     */
    public function index()
    {
        // 过滤请求
        $this->request->filter(['strip_tags','trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where,$sort,$order,$offset,$limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort,$order)
                ->paginate($limit);

            // 追加 B 端名称字段
            foreach ($list as $row) {
                $row->visible(['id','rule_name','is_decrease','rule_type_text','vesting_days','vesting_percentage','execution_type_text','execution_time','status_text','created_at']);
            }

            $result = ["total"=>$list->total(),"rows"=>$list->items()];
            return json($result);
        }
        return $this->view->fetch();
    }
} 