<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'connector'  => 'Redis',         // 驱动类型，可选择 Redis 或者 Database
    'expire'     => 60,              // 任务的过期时间，默认为60秒
    'default'    => 'default',       // 默认队列名称
    'host'       => '127.0.0.1',     // Redis 主机地址
    'port'       => 6379,            // Redis 端口
    'password'   => '',              // Redis 密码
    'select'     => 0,               // Redis 数据库
    'timeout'    => 0,               // Redis 超时时间
    'persistent' => false,           // Redis 持久连接

    // 队列类型定义
    'queues'     => [
        // 默认队列
        'default' => [
            'delay' => 0,  // 延迟执行的秒数
        ],
        
        // 解禁任务队列
        'vesting' => [
            'delay' => 0,  // 延迟由任务自身控制
            'sleep' => 3,  // 队列处理完后等待时间
        ],
    ],
];
