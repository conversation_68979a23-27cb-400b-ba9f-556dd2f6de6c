<?php
namespace app\common\model;

use think\Model;

/**
 * 公告审批记录模型
 * 该模型用于记录公告从提交到最终审批的完整流程。
 */
class EsopNoticeApprovalRecord extends Model
{
    // 指定模型对应的表名
    protected $name = 'esop_notice_approval_records';

    // 关闭自动时间戳，手动维护 created_at、updated_at 字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    /**
     * 关联公告
     * 一个审批记录对应一篇公告
     */
    public function notice()
    {
        return $this->belongsTo('EsopArticle', 'notice_id', 'id');
    }

    /**
     * 关联用户
     * 一个审批记录由提交该公告的用户创建
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }

    /**
     * 创建待审批记录
     *
     * @param int $noticeId 公告 ID
     * @param int $userId   用户 ID
     * @return bool         是否创建成功
     */
    public static function createPending(int $noticeId, int $userId): bool
    {
        $model             = new self();
        $model->notice_id  = $noticeId;
        $model->user_id    = $userId;
        $model->status     = 0; // 0 = 待审批
        $model->created_at = date('Y-m-d H:i:s');
        return (bool)$model->save();
    }
} 