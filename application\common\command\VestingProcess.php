<?php

namespace app\common\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Exception;
use think\Queue;

/**
 * 解禁处理命令
 * 该命令用于管理授权解禁任务，可创建初始任务或手动触发处理
 */
class VestingProcess extends Command
{
    protected function configure()
    {
        $this->setName('vesting:process')
             ->setDescription('管理授权解禁任务')
             ->addOption('create-tasks', null, null, '创建所有规则的初始任务')
             ->addOption('force-run', null, null, '强制立即执行所有待执行的解禁任务');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            // 检查是否为创建任务模式
            if ($input->hasOption('create-tasks')) {
                $this->createInitialTasks($output);
                return;
            }
            
            // 检查是否为强制执行模式
            if ($input->hasOption('force-run')) {
                $this->forceRunPendingTasks($output);
                return;
            }
            
            // 默认情况下，创建任务并显示状态信息
            $this->showTasksStatus($output);
            
        } catch (Exception $e) {
            $output->error('解禁处理主程序异常: ' . $e->getMessage());
            
            // 记录系统日志
            Db::name('esop_system_logs')->insert([
                'operation' => '解禁处理主程序异常',
                'ip_address' => '127.0.0.1',
                'status' => 0,
                'detail' => $e->getMessage(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 创建所有规则的初始队列任务
     * @param Output $output 输出对象
     */
    private function createInitialTasks(Output $output)
    {
        try {
            // 获取所有启用的解禁规则
            $rules = Db::name('esop_vesting_rules')
                ->where('status', 1)
                ->select();
            
            $today = date('Y-m-d');
            $taskCount = 0;
            
            foreach ($rules as $rule) {
                // 检查该规则是否有待解禁记录
                $hasRecords = Db::name('esop_vesting_records')
                    ->alias('r')
                    ->join('fa_esop_grant_operation_logs g', 'r.grant_id = g.id')
                    ->where('g.vesting_rule_id', $rule['id'])
                    ->where('r.vesting_date', $today)
                    ->where('r.vesting_status', 0)
                    ->count();
                
                if ($hasRecords > 0) {
                    // 解析执行时间
                    $executionTime = $this->parseExecutionTime($rule['execution_time'], $today);
                    $delay = $this->calculateDelaySeconds($executionTime);
                    
                    $jobData = [
                        'rule_id' => $rule['id'],
                        'execution_date' => $today
                    ];
                    
                    // 推送到队列，设置延迟执行
                    Queue::later($delay, 'app\\common\\job\\VestingExecutionJob', $jobData, 'vesting');
                    $taskCount++;
                    
                    $output->writeln("已为规则 [{$rule['rule_name']}] 创建执行任务，执行时间: {$executionTime}");
                }
            }
            
            $output->writeln("任务创建完成，总计: {$taskCount} 个任务");
            
        } catch (Exception $e) {
            $output->error('创建初始任务异常: ' . $e->getMessage());
            
            // 记录系统日志
            Db::name('esop_system_logs')->insert([
                'operation' => '创建初始解禁任务异常',
                'ip_address' => '127.0.0.1',
                'status' => 0,
                'detail' => $e->getMessage(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 强制执行所有待处理的解禁任务
     * @param Output $output 输出对象
     */
    private function forceRunPendingTasks(Output $output)
    {
        try {
            $today = date('Y-m-d');
            
            // 获取所有规则
            $rules = Db::name('esop_vesting_rules')
                ->where('status', 1)
                ->select();
            
            $processedCount = 0;
            
            foreach ($rules as $rule) {
                // 检查是否已执行
                $executed = Db::name('esop_vesting_execution_logs')
                    ->where('rule_id', $rule['id'])
                    ->where('execution_date', $today)
                    ->find();
                
                if (!$executed) {
                    // 创建并立即执行任务（延迟1秒）
                    $jobData = [
                        'rule_id' => $rule['id'],
                        'execution_date' => $today
                    ];
                    
                    Queue::later(1, 'app\\common\\job\\VestingExecutionJob', $jobData, 'vesting');
                    $processedCount++;
                    
                    $output->writeln("已强制触发规则 [{$rule['rule_name']}] 的执行任务");
                }
            }
            
            $output->writeln("强制执行完成，总计: {$processedCount} 个任务");
            
        } catch (Exception $e) {
            $output->error('强制执行任务异常: ' . $e->getMessage());
            
            // 记录系统日志
            Db::name('esop_system_logs')->insert([
                'operation' => '强制执行解禁任务异常',
                'ip_address' => '127.0.0.1',
                'status' => 0,
                'detail' => $e->getMessage(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    /**
     * 显示任务状态信息
     * @param Output $output 输出对象
     */
    private function showTasksStatus(Output $output)
    {
        $today = date('Y-m-d');
        
        // 获取所有规则
        $rules = Db::name('esop_vesting_rules')
            ->where('status', 1)
            ->select();
        
        $output->writeln("========== 解禁任务状态 ==========");
        $output->writeln("日期: {$today}");
        $output->writeln("");
        
        foreach ($rules as $rule) {
            $output->writeln("规则: [{$rule['rule_name']}]");
            
            // 检查是否已执行
            $executed = Db::name('esop_vesting_execution_logs')
                ->where('rule_id', $rule['id'])
                ->where('execution_date', $today)
                ->find();
            
            if ($executed) {
                $output->writeln("- 状态: 已执行");
                $output->writeln("- 执行时间: {$executed['execution_time']}");
                $output->writeln("- 结果: 总计{$executed['total_count']}条, 成功{$executed['success_count']}条, 失败{$executed['fail_count']}条");
            } else {
                // 检查待执行记录数
                $pendingCount = Db::name('esop_vesting_records')
                    ->alias('r')
                    ->join('fa_esop_grant_operation_logs g', 'r.grant_id = g.id')
                    ->where('g.vesting_rule_id', $rule['id'])
                    ->where('r.vesting_date', $today)
                    ->where('r.vesting_status', 0)
                    ->count();
                
                if ($pendingCount > 0) {
                    $executionTime = $this->parseExecutionTime($rule['execution_time'], $today);
                    $output->writeln("- 状态: 待执行");
                    $output->writeln("- 计划执行时间: {$executionTime}");
                    $output->writeln("- 待处理记录: {$pendingCount}条");
                } else {
                    $output->writeln("- 状态: 无需执行");
                    $output->writeln("- 原因: 无待处理记录");
                }
            }
            
            $output->writeln("");
        }
        
        $output->writeln("========== 结束 ==========");
    }
    
    /**
     * 解析执行时间字符串
     * @param string $executionTimeStr 执行时间字符串（如：1:30）
     * @param string $date 日期字符串
     * @return string 完整的执行时间戳
     */
    private function parseExecutionTime($executionTimeStr, $date)
    {
        // 默认时间设为0:00
        $hour = 0;
        $minute = 0;
        
        // 解析时间字符串
        if (preg_match('/(\d+):(\d+)/', $executionTimeStr, $matches)) {
            $hour = (int)$matches[1];
            $minute = (int)$matches[2];
        }
        
        // 返回完整的执行时间
        return $date . ' ' . sprintf('%02d:%02d:00', $hour, $minute);
    }
    
    /**
     * 计算延迟执行的秒数
     * @param string $executionTime 目标执行时间
     * @return int 需要延迟的秒数
     */
    private function calculateDelaySeconds($executionTime)
    {
        $targetTimestamp = strtotime($executionTime);
        $currentTimestamp = time();
        
        $delay = $targetTimestamp - $currentTimestamp;
        
        // 如果目标时间已过，设置为立即执行(延迟1秒)
        return max(1, $delay);
    }
} 