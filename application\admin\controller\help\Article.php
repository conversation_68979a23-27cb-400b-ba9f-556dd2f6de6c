<?php

namespace app\admin\controller\help;

use app\common\controller\Backend;
use app\common\model\EsopArticle;
use app\common\model\EsopArticleCategory;

/**
 * 帮助中心文章管理
 * @icon fa fa-file-text
 */
class Article extends Backend
{
    protected $model = null;
    protected $sort = 'id';
    protected $order = 'DESC';
    protected $boardId = 0; // 帮助中心

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopArticle();

        // 分类下拉
        $cats = EsopArticleCategory::where('board_id', $this->boardId)->column('category_name','id');
        $this->view->assign('categoryList', $cats);
        $this->view->assign('statusList', [1=>'发布',0=>'草稿']);
        $this->view->assign('topList', [0=>'否',1=>'是']);
    }

    public function index()
    {
        $this->request->filter(['strip_tags','trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {return $this->selectpage();}
            list($where,$sort,$order,$offset,$limit)=$this->buildparams();
            $list = $this->model
                ->with(['category','baccount'])
                ->where($where)
                ->where('board_id',$this->boardId)
                ->order($sort,$order)
                ->paginate($limit);
            $rows = [];
            foreach ($list as $row) {
                $rows[] = [
                    'id'            => $row->id,
                    'title'         => $row->title,
                    'category_id'   => $row->category_id,
                    'category_name' => $row->category? $row->category->category_name:'',
                    'b_account_id'  => $row->b_account_id,
                    'b_account_name'=> $row->baccount? $row->baccount->account_name:'通用',
                    'status'        => $row->status,
                    'status_text'   => $row->status? '发布':'草稿',
                    'is_top'        => $row->is_top,
                    'is_top_text'   => $row->is_top? '是':'否',
                    'sort_order'    => $row->sort_order,
                    'created_at'    => $row->created_at,
                ];
            }
            return json(['total'=>$list->total(),'rows'=>$rows]);
        }
        return $this->view->fetch();
    }

    public function add()
    {
        if($this->request->isPost()){
            $params=$this->request->post('row/a',[],'trim');
            $params['board_id']=$this->boardId;
            $params['author_id']=$this->auth->id;
            $this->request->post(['row'=>$params]);
        }
        return parent::add();
    }
} 