<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Feedback_title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$row.feedback_title}</p>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Feedback_content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$row.feedback_content}</p>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Feedback_images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <div class="gallery-container">
                    {foreach $row.feedback_images as $image}
                    {if $image}
                    <div class="gallery-image">
                        <a href="{$image|cdnurl}" data-toggle="lightbox" data-group="gallery">
                            <img src="{$image|cdnurl}" class="img-responsive" style="max-height:100px;">
                        </a>
                    </div>
                    {/if}
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['0'=>__('Status 0'), '1'=>__('Status 1')], $row['status'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Handle_result')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-handle_result" class="form-control" name="row[handle_result]" rows="5">{$row.handle_result}</textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 