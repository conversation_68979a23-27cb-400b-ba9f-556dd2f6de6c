<?php

namespace app\admin\controller\baccount;

use app\common\controller\Backend;
use app\common\model\UiStyle;
use app\common\model\EsopGrantStatistics;
use think\Db;
use think\Exception;
use think\exception\ValidateException;
use think\exception\PDOException;

/**
 * B端账户管理
 *
 * @icon fa fa-building
 */
class Account extends Backend
{
    /**
     * EsopBAccount模型对象
     * @var \app\common\model\EsopBAccount
     */
    protected $model = null;
    
    /**
     * 默认排序方式
     */
    protected $sort = 'id';
    protected $order = 'DESC';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\EsopBAccount;
        
        // 从数据库获取UI风格列表
        // 从数据库获取UI风格列表，按照id升序排列，并以id为键，style_name为值生成数组
        $uiStyles = UiStyle::order('id', 'asc')->column('style_name', 'id');
        
        // 如果没有记录，则使用默认值
        if (empty($uiStyles)) {
            $uiStyles = [];
        }
        
        // 获取解禁规则列表（用于下拉选择框）
        $vestingRuleList = \app\common\model\EsopVestingRule::where('status', 1)
            ->column('rule_name', 'id');
        
        // 获取解禁规则详细信息（用于表格展示）
        $vestingRules = \app\common\model\EsopVestingRule::where('status', 1)
            ->select();
        
        $this->view->assign("uiStyleList", $uiStyles);
        $this->view->assign("vestingRuleList", $vestingRuleList);
        $this->view->assign("vestingRules", $vestingRules);
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            foreach ($list as $row) {
                $row->team_member_count = $row->getTeamMemberCount();

                // 组装 B 端管理员显示文本: 多个用户真实姓名+手机号，逗号分隔
                $adminArr = [];
                $usersInfo = $row->getUsersInfo();
                foreach ($usersInfo as $info) {
                    $adminArr[] = ($info['real_name'] ? $info['real_name'] : '') . ' (' . $info['phone'] . ')';
                }
                $row->admin_display = implode('，', array_filter($adminArr));
            }
            
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                // 处理user_ids，将数组转换为逗号分隔的字符串
                if (isset($params['user_ids']) && is_array($params['user_ids'])) {
                    $params['user_ids'] = implode(',', $params['user_ids']);
                }
                // 处理vesting_rule_ids，将数组转换为逗号分隔的字符串
                if (isset($params['vesting_rule_ids']) && is_array($params['vesting_rule_ids'])) {
                    $params['vesting_rule_ids'] = implode(',', $params['vesting_rule_ids']);
                }
                
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    
                    // 如果available_assets大于0，添加资产变更记录，金额处理使用高精度bc函数
                    if ($result !== false && isset($params['available_assets']) && bccomp((string)$params['available_assets'], '0', 8) > 0) {
                        // 使用bc函数处理金额，保留8位小数
                        $availableAssets = bcadd((string)$params['available_assets'], '0', 8);
                        $logData = [
                            'b_account_id'    => $this->model->id, // 获取新插入的B端账户ID
                            'change_type'     => 1, // 1-增加
                            'change_amount'   => $availableAssets, // 变更金额
                            'before_amount'   => '0.********', // 初始资产为0，字符串格式
                            'after_amount'    => $availableAssets, // 变更后金额
                            'operator_id'     => $this->auth->id,
                            'operation_notes' => '新建B端账户初始资产',
                            'created_at'      => date('Y-m-d H:i:s')
                        ];
                        // 插入资产变更日志
                        Db::name('esop_b_account_asset_logs')->insert($logData);
                        
                        // 记录授权统计
                        $operatorInfo = \app\admin\model\Admin::get($this->auth->id);
                        $operatorName = $operatorInfo ? $operatorInfo->nickname : '';
                        
                        $statisticsData = [
                            'account_id' => $this->model->id,
                            'account_name' => $params['account_name'],
                            'account_type' => 1, // 1-B端账户
                            'grant_amount' => $availableAssets,
                            'operator_id' => $this->auth->id,
                            'operator_name' => $operatorName,
                            'after_available_assets' => $availableAssets,
                            'after_pending_assets' => 0, // B端账户没有待解冻资产概念
                            'remark' => '创建B端账户初始授权资产',
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                        
                        EsopGrantStatistics::addRecord($statisticsData);
                    }
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        // 生成admin_display用于回显，支持多个用户
        $adminArr = [];
        $usersInfo = $row->getUsersInfo();
        foreach ($usersInfo as $info) {
            $adminArr[] = ($info['real_name'] ? $info['real_name'] : '') . ' (' . $info['phone'] . ')';
        }
        $row->admin_display = implode('，', array_filter($adminArr));
        // user_ids转为数组，便于表单回显
        $row->user_ids = $row->getUserIdsArray();
        // vesting_rule_ids转为数组，便于表单回显
        $row->vesting_rule_ids = $row->getVestingRuleIdsArray();
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 处理user_ids，将数组转换为逗号分隔的字符串
                if (isset($params['user_ids']) && is_array($params['user_ids'])) {
                    $params['user_ids'] = implode(',', $params['user_ids']);
                }
                // 处理vesting_rule_ids，将数组转换为逗号分隔的字符串
                if (isset($params['vesting_rule_ids']) && is_array($params['vesting_rule_ids'])) {
                    $params['vesting_rule_ids'] = implode(',', $params['vesting_rule_ids']);
                }
                
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 资产管理
     */
    public function assets($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        // 生成admin_display用于回显，支持多个用户
        $adminArr = [];
        $usersInfo = $row->getUsersInfo();
        foreach ($usersInfo as $info) {
            $adminArr[] = ($info['real_name'] ? $info['real_name'] : '') . ' (' . $info['phone'] . ')';
        }
        $row->admin_display = implode('，', array_filter($adminArr));
        
        if ($this->request->isPost()) {
            $params = $this->request->post();
            if ($params) {
                // 验证必填参数
                if (!isset($params['change_type']) || !isset($params['change_amount']) || $params['change_amount'] <= 0) {
                    $this->error(__('参数错误'));
                }

                $changeType = intval($params['change_type']); // 1-增加，2-减少
                $changeAmount = floatval($params['change_amount']);
                $notes = isset($params['operation_notes']) ? $params['operation_notes'] : '';
                
                // 获取变更前金额
                $beforeAmount = $row->available_assets;
                
                // 计算变更后金额
                $afterAmount = $beforeAmount;
                if ($changeType == 1) {
                    // 增加资产
                    $afterAmount = $beforeAmount + $changeAmount;
                } else if ($changeType == 2) {
                    // 减少资产
                    if ($beforeAmount < $changeAmount) {
                        $this->error(__('可授权资产不足'));
                    }
                    $afterAmount = $beforeAmount - $changeAmount;
                } else {
                    $this->error(__('无效的变更类型'));
                }
                
                Db::startTrans();
                try {
                    // 使用乐观锁方法更新
                    $result = \app\common\model\EsopBAccount::updateAccountAsset($row->id, [
                        'available_assets' => $changeType == 1 ? $changeAmount : '-' . $changeAmount
                    ]);
                    
                    // 2. 记录资产变更日志
                    $logData = [
                        'b_account_id' => $row->id,
                        'change_type' => $changeType,
                        'change_amount' => $changeAmount,
                        'before_amount' => $beforeAmount,
                        'after_amount' => $afterAmount,
                        'operator_id' => $this->auth->id,
                        'operation_notes' => $notes,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    \app\common\model\EsopBAccountAssetLog::create($logData);
                    
                    // 3. 记录授权统计
                    if ($changeType == 1) { // 只有增加资产时才记录授权统计
                        // 获取操作人信息
                        $operatorInfo = \app\admin\model\Admin::get($this->auth->id);
                        $operatorName = $operatorInfo ? $operatorInfo->nickname : '';
                        
                        $statisticsData = [
                            'account_id' => $row->id,
                            'account_name' => $row->account_name,
                            'account_type' => 1, // 1-B端账户
                            'grant_amount' => $changeAmount,
                            'operator_id' => $this->auth->id,
                            'operator_name' => $operatorName,
                            'after_available_assets' => $afterAmount,
                            'after_pending_assets' => 0, // B端账户没有待解冻资产概念
                            'remark' => '后台管理系统增加B端账户资产：' . $notes,
                            'created_at' => date('Y-m-d H:i:s')
                        ];
                        
                        EsopGrantStatistics::addRecord($statisticsData);
                    }
                    
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('操作失败'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 停用B端
     * （该功能已根据需求移除）
     */
    // public function disable($ids = null)
    // {
    //     // 停用功能已移除
    //     $this->error('该功能已被禁用');
    // }

    /**
     * 启用B端
     * （该功能已根据需求移除）
     */
    // public function enable($ids = null)
    // {
    //     // 启用功能已移除
    //     $this->error('该功能已被禁用');
    // }
} 