<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('B端名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_name" class="form-control" name="row[account_name]" type="text" value="{$row.account_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('授权资产别名')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-alias" class="form-control" name="row[alias]" type="text" value="{$row.alias|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('B端管理员')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_ids" data-rule="required" data-source="esopuser/user/select" data-field="nickname" data-search-field="phone,real_name" class="form-control selectpage" name="row[user_ids][]" type="text" value="{:implode(',', $row.user_ids)}" data-multiple="true">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('授权平台审核')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="row[is_audit]-0"><input id="row[is_audit]-0" name="row[is_audit]" type="radio" value="0" {in name="row.is_audit" value="0"}checked{/in} /> {:__('否')}</label> 
                <label for="row[is_audit]-1"><input id="row[is_audit]-1" name="row[is_audit]" type="radio" value="1" {in name="row.is_audit" value="1"}checked{/in} /> {:__('是')}</label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('UI风格')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="uiStyleList" item="vo" key="key"}
                <label for="row[ui_style_id]-{$key}"><input id="row[ui_style_id]-{$key}" name="row[ui_style_id]" type="radio" value="{$key}" {in name="key" value="$row.ui_style_id"}checked{/in} /> {$vo}</label> 
                {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('行权手续费率')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-exercise_fee_rate" class="form-control" step="0.01" name="row[exercise_fee_rate]" type="number" value="{$row.exercise_fee_rate}">
                <span class="input-group-addon">%</span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('解禁规则设置')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th width="50">{:__('选择')}</th>
                            <th>{:__('规则名称')}</th>
                            <th>{:__('是否递减')}</th>
                            <th>{:__('解禁计划')}</th>
                            <th>{:__('解禁执行时间')}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {foreach name="vestingRules" item="rule"}
                        <tr>
                            <td>
                                <input type="checkbox" name="row[vesting_rule_ids][]" value="{$rule.id}" {if $row.vesting_rule_ids && in_array($rule.id, $row.vesting_rule_ids)}checked{/if} />
                            </td>
                            <td>{$rule.rule_name}</td>
                            <td>{$rule.is_decrease ? '是' : '否'}</td>
                            <td>
                                {if $rule.rule_type == 1}
                                    {$rule.vesting_days}天
                                {else}
                                    {$rule.vesting_percentage}%
                                {/if}
                            </td>
                            <td>{$rule.execution_time}</td>
                        </tr>
                        {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>