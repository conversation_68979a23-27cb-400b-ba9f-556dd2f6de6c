define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 设置表单验证
            $("form.edit-form").data("validator-options", {
                display: function (elem) {
                    return $(elem).closest('tr').find("td:first").text();
                }
            });
            
            // 绑定表单验证事件
            Form.api.bindevent($('#config-form'));
            Form.api.bindevent($('#third-form'));
            Form.api.bindevent($('#about-form'));
            Form.api.bindevent($('#delete-risk-form'));
            Form.api.bindevent($('#privacy-policy-form'));
            Form.api.bindevent($('#user-agreement-form'));
            
            // 初始化富文本编辑器
            Controller.initEditor();
            
            // 初始化上传组件
            Controller.initUpload();

            // 初始化APP上传组件
            Controller.initAppUpload();
            
            // 选项卡切换事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                // 触发窗口大小调整以修复一些自适应问题
                $(window).trigger('resize');
            });
        },
        
        // 初始化富文本编辑器
        initEditor: function() {
            require(['summernote'], function () {
                var aboutEditor = $('#c-about-us');
                if (aboutEditor.length > 0) {
                    aboutEditor.summernote({
                        height: 500,
                        lang: 'zh-CN',
                        fontNames: ['Arial', 'Arial Black', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana', 'Microsoft YaHei'],
                        callbacks: {
                            // 上传图片的回调
                            onImageUpload: function (files) {
                                var that = this;
                                // 上传代码
                                var data = new FormData();
                                data.append("file", files[0]);
                                $.ajax({
                                    url: Config.upload.uploadurl,
                                    type: "POST",
                                    data: data,
                                    cache: false,
                                    contentType: false,
                                    processData: false,
                                    success: function (ret) {
                                        if (ret.hasOwnProperty("code") && ret.code == 1) {
                                            $(that).summernote('insertImage', ret.url);
                                        } else {
                                            Toastr.error(ret.msg);
                                        }
                                    },
                                    error: function (e) {
                                        Toastr.error("上传失败");
                                    }
                                });
                            }
                        }
                    });
                }
                // 新增：注销风险提示内容富文本编辑器
                var deleteRiskEditor = $('#c-delete-risk-tips');
                if (deleteRiskEditor.length > 0) {
                    deleteRiskEditor.summernote({
                        height: 500,
                        lang: 'zh-CN',
                        fontNames: ['Arial', 'Arial Black', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana', 'Microsoft YaHei'],
                        callbacks: {
                            onImageUpload: function (files) {
                                var that = this;
                                var data = new FormData();
                                data.append("file", files[0]);
                                $.ajax({
                                    url: Config.upload.uploadurl,
                                    type: "POST",
                                    data: data,
                                    cache: false,
                                    contentType: false,
                                    processData: false,
                                    success: function (ret) {
                                        if (ret.hasOwnProperty("code") && ret.code == 1) {
                                            $(that).summernote('insertImage', ret.url);
                                        } else {
                                            Toastr.error(ret.msg);
                                        }
                                    },
                                    error: function (e) {
                                        Toastr.error("上传失败");
                                    }
                                });
                            }
                        }
                    });
                }

                // 新增：隐私政策富文本编辑器
                var privacyPolicyEditor = $('#c-privacy-policy');
                if (privacyPolicyEditor.length > 0) {
                    privacyPolicyEditor.summernote({
                        height: 500,
                        lang: 'zh-CN',
                        fontNames: ['Arial', 'Arial Black', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana', 'Microsoft YaHei'],
                        callbacks: {
                            onImageUpload: function (files) {
                                var that = this;
                                var data = new FormData();
                                data.append("file", files[0]);
                                $.ajax({
                                    url: Config.upload.uploadurl,
                                    type: "POST",
                                    data: data,
                                    cache: false,
                                    contentType: false,
                                    processData: false,
                                    success: function (ret) {
                                        if (ret.hasOwnProperty("code") && ret.code == 1) {
                                            $(that).summernote('insertImage', ret.url);
                                        } else {
                                            Toastr.error(ret.msg);
                                        }
                                    },
                                    error: function (e) {
                                        Toastr.error("上传失败");
                                    }
                                });
                            }
                        }
                    });
                }

                // 新增：用户协议富文本编辑器
                var userAgreementEditor = $('#c-user-agreement');
                if (userAgreementEditor.length > 0) {
                    userAgreementEditor.summernote({
                        height: 500,
                        lang: 'zh-CN',
                        fontNames: ['Arial', 'Arial Black', 'Courier New', 'Helvetica', 'Impact', 'Tahoma', 'Times New Roman', 'Verdana', 'Microsoft YaHei'],
                        callbacks: {
                            onImageUpload: function (files) {
                                var that = this;
                                var data = new FormData();
                                data.append("file", files[0]);
                                $.ajax({
                                    url: Config.upload.uploadurl,
                                    type: "POST",
                                    data: data,
                                    cache: false,
                                    contentType: false,
                                    processData: false,
                                    success: function (ret) {
                                        if (ret.hasOwnProperty("code") && ret.code == 1) {
                                            $(that).summernote('insertImage', ret.url);
                                        } else {
                                            Toastr.error(ret.msg);
                                        }
                                    },
                                    error: function (e) {
                                        Toastr.error("上传失败");
                                    }
                                });
                            }
                        }
                    });
                }
            });
        },
        
        initUpload: function () {
            // 初始化LOGO上传
            var faupload = $('#faupload-image');
            if (faupload.length > 0) {
                require(['upload'], function (Upload) {
                    Upload.api.plupload('#faupload-image', function (data, ret) {
                        var url = ret.url || ret.data.url;
                        $('#c-logo').val(url);
                        Toastr.success('上传成功');
                    });
                });
            }
            
            // 初始化LOGO选择器
            var fachoose = $('#fachoose-image');
            if (fachoose.length > 0) {
                require(['upload'], function (Upload) {
                    Upload.api.plupload('#fachoose-image', function (data, ret) {
                        var url = ret.url || ret.data.url;
                        $('#c-logo').val(url);
                        Toastr.success('选择成功');
                    });
                });
            }
            
            // 监听LOGO预览
            $('#c-logo').on('change', function () {
                var url = $(this).val();
                if (url) {
                    $('#p-logo').html('<li class="col-xs-3"><a href="' + url + '" target="_blank" class="thumbnail"><img src="' + url + '" class="img-responsive"></a></li>');
                } else {
                    $('#p-logo').empty();
                }
            });
            
            // 初始加载LOGO预览
            $('#c-logo').trigger('change');
        },

        // 初始化APP上传组件
        initAppUpload: function () {
            // 安卓APP上传
            $('#upload-android-app').on('click', function () {
                var inputId = $(this).data('input-id');
                var appType = $(this).data('app-type');
                Controller.uploadAppFile(inputId, appType);
            });

            // iOS APP上传
            $('#upload-ios-app').on('click', function () {
                var inputId = $(this).data('input-id');
                var appType = $(this).data('app-type');
                Controller.uploadAppFile(inputId, appType);
            });
        },

        // APP文件上传处理
        uploadAppFile: function (inputId, appType) {
            // 创建文件选择器
            var fileInput = $('<input type="file" style="display:none;">');

            // 根据应用类型设置文件类型限制
            if (appType === 'android') {
                fileInput.attr('accept', '.apk');
            } else if (appType === 'ios') {
                fileInput.attr('accept', '.ipa');
            }

            // 绑定文件选择事件
            fileInput.on('change', function () {
                var file = this.files[0];
                if (!file) {
                    return;
                }

                // 验证文件类型
                var fileName = file.name.toLowerCase();
                var isValidFile = false;

                if (appType === 'android' && fileName.endsWith('.apk')) {
                    isValidFile = true;
                } else if (appType === 'ios' && fileName.endsWith('.ipa')) {
                    isValidFile = true;
                }

                if (!isValidFile) {
                    Toastr.error('请选择正确的' + (appType === 'android' ? 'APK' : 'IPA') + '文件');
                    return;
                }

                // 显示上传进度
                var uploadBtn = $('#upload-' + appType + '-app');
                var originalText = uploadBtn.html();
                uploadBtn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 上传中...');

                // 创建FormData对象
                var formData = new FormData();
                formData.append('file', file);
                formData.append('app_type', appType);

                // 上传文件
                $.ajax({
                    url: 'system/config/uploadApp',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        uploadBtn.prop('disabled', false).html(originalText);

                        if (response.code === 1) {
                            // 上传成功，填充下载地址
                            $('#' + inputId).val(response.url);
                            Toastr.success('上传成功');
                        } else {
                            Toastr.error(response.msg || '上传失败');
                        }
                    },
                    error: function () {
                        uploadBtn.prop('disabled', false).html(originalText);
                        Toastr.error('上传失败，请重试');
                    }
                });
            });

            // 触发文件选择
            fileInput.click();
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
