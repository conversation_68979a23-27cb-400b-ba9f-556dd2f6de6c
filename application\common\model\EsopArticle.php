<?php
namespace app\common\model;

use think\Model;

/**
 * 文章模型
 */
class EsopArticle extends Model
{
    protected $name = 'esop_articles';

    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    /** 关联分类 */
    public function category()
    {
        return $this->belongsTo('EsopArticleCategory','category_id','id');
    }

    /** 关联 B 端账户 */
    public function baccount()
    {
        return $this->belongsTo('EsopBAccount','b_account_id','id');
    }

    /** 作者 (管理员) 关联 可选*/
    public function author()
    {
        return $this->belongsTo('Admin','author_id','id');
    }
} 