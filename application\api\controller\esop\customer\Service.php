<?php

namespace app\api\controller\esop\customer;

use app\common\controller\Api;

/**
 * 客服管理接口
 */
class Service extends Api
{
    // 无需登录的接口,*表示全部
    protected $noNeedLogin = ['index'];
    
    // 无需鉴权的接口,*表示全部
    protected $noNeedRight = ['*'];
    
    /**
     * 获取客服列表
     *
     * @param int $page 页码
     * @param int $limit 每页条数
     */
    public function index()
    {
        $page = $this->request->request('page', 1, 'intval');
        $limit = $this->request->request('limit', 10, 'intval');
        
        $model = new \app\admin\model\customer\Service();
        
        $list = $model->order('id desc')
            ->paginate($limit, false, ['page' => $page]);

        $rows = $list->items();
            
        // 处理列表数据，确保字段格式正确
        $rows = collection($rows)->toArray();
        foreach ($rows as &$item) {
            // 确保图标路径是完整URL
            if ($item['icon'] && !preg_match('/^(http|https):\/\//i', $item['icon'])) {
                $item['icon'] = request()->domain() . $item['icon'];
            }
        }
        
        $result = [
            'code' => 1, 
            'msg' => '获取成功',
            'time' => time(),
            'data' => [
                'total' => $list->total(),
                'per_page' => $limit,
                'current_page' => $page,
                'last_page' => $list->lastPage(),
                'rows' => $rows
            ]
        ];
        
        return json($result);
    }
} 