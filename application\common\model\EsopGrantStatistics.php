<?php

namespace app\common\model;

use think\Model;

/**
 * 授权统计模型
 */
class EsopGrantStatistics extends Model
{
    // 表名
    protected $name = 'esop_grant_statistics';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = false;
    
    // 追加属性
    protected $append = [
        
    ];
    
    /**
     * 添加授权统计记录
     * 
     * @param array $data 授权统计数据
     * @return EsopGrantStatistics
     */
    public static function addRecord($data)
    {
        $model = new self();
        $model->save($data);
        return $model;
    }
    
    /**
     * 批量添加授权统计记录
     * 
     * @param array $dataList 授权统计数据列表
     * @return boolean
     */
    public static function addBatchRecords($dataList)
    {
        return (new self())->saveAll($dataList);
    }
    
    /**
     * 获取B端账户授权统计
     * 
     * @param int $accountId B端账户ID
     * @param string $startDate 开始日期，格式：Y-m-d
     * @param string $endDate 结束日期，格式：Y-m-d
     * @return array
     */
    public static function getBAccountStatistics($accountId, $startDate = null, $endDate = null)
    {
        $query = self::where('operator_id', $accountId)
            ->where('account_type', 2); // 只查询授权给C端用户的记录
            
        if ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }
        
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        
        return $query->order('created_at', 'desc')->select();
    }
    
    /**
     * 获取用户授权统计
     * 
     * @param int $userId 用户ID
     * @param string $startDate 开始日期，格式：Y-m-d
     * @param string $endDate 结束日期，格式：Y-m-d
     * @return array
     */
    public static function getUserStatistics($userId, $startDate = null, $endDate = null)
    {
        $query = self::where('account_id', $userId)
            ->where('account_type', 2); // 只查询C端用户的记录
            
        if ($startDate) {
            $query->where('created_at', '>=', $startDate . ' 00:00:00');
        }
        
        if ($endDate) {
            $query->where('created_at', '<=', $endDate . ' 23:59:59');
        }
        
        return $query->order('created_at', 'desc')->select();
    }
} 