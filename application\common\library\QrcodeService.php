<?php

namespace app\common\library;

use think\facade\Request;

/**
 * 二维码生成服务
 *
 * 为避免引入额外依赖，本实现调用在线接口生成二维码图片，
 * 将图片保存到 public/uploads/qrcodes/YYYYMMDD 目录下，
 * 返回相对路径，调用方可自行组合完整 URL。
 */
class QrcodeService
{
    /**
     * 生成二维码
     *
     * @param string $text 需要编码的文本内容
     * @param int    $size 图片尺寸（像素），默认 300
     * @return string 相对路径，如 /uploads/qrcodes/20250706/xxxx.png
     * @throws \Exception
     */
    public static function generate(string $text, int $size = 300): string
    {
        // 目录：/public/uploads/qrcodes/20250706/
        $dateDir      = date('Ymd');
        $relativeDir  = '/uploads/qrcodes/' . $dateDir . '/';
        $absDir       = ROOT_PATH . 'public' . DIRECTORY_SEPARATOR . 'uploads' . DIRECTORY_SEPARATOR . 'qrcodes' . DIRECTORY_SEPARATOR . $dateDir . DIRECTORY_SEPARATOR;

        // 创建目录
        if (!is_dir($absDir) && !mkdir($absDir, 0755, true) && !is_dir($absDir)) {
            throw new \Exception('无法创建二维码目录');
        }

        // 文件名
        $filename      = md5($text . microtime(true)) . '.png';
        $absPath       = $absDir . $filename;
        $relativePath  = $relativeDir . $filename;

        // 利用 addons/qrcode 插件生成二维码
        try {
            // 构造插件参数
            $params = [
                'text'     => $text,
                'format'   => 'png',        // 生成 PNG
                'size'     => $size,        // 图片尺寸
                'padding'  => 2,            // 安全边距，避免扫描问题
                // 其余参数使用插件默认配置
            ];

            /** @var \Endroid\QrCode\Writer\Result\ResultInterface $result */
            $result = \addons\qrcode\library\Service::qrcode($params);
            // 保存到目标文件
            $result->saveToFile($absPath);
        } catch (\Throwable $e) {
            throw new \Exception('二维码生成失败: ' . $e->getMessage());
        }

        return $relativePath; // 形如 /uploads/qrcodes/20250706/xxxx.png
    }
} 