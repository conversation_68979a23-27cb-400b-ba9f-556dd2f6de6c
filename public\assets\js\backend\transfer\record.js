define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'transfer/record/index',
                    detail_url: 'transfer/record/detail',
                    approve_url: 'transfer/record/approve'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                sortOrder: 'desc',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true},
                        {field: 'user_id', title: __('From user'), formatter: function(value, row, index) {
                            return row.user_profile.real_name + ' ' + (row.user.phone ? row.user.phone : '');
                        }},
                        {field: 'target_user_id', title: __('To user'), formatter: function(value, row, index) {
                            return row.target_user_profile.real_name + ' ' + (row.target_user ? row.target_user.phone : '');
                        }},
                        {field: 'amount', title: __('Amount'), operate:'BETWEEN'},
                        {field: 'remark', title: __('Remark'), operate:'LIKE'},
                        {field: 'approval_status', title: __('Approval status'), searchList: {"0":__('Pending'),"1":__('Approved'),"2":__('Rejected')}, formatter: Table.api.formatter.status},
                        {field: 'approval_time', title: __('Approval time'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'created_at', title: __('Create time'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, 
                            events: Table.api.events.operate, 
                            formatter: Table.api.formatter.operate,
                            buttons: [{
                                name: 'approve',
                                title: '审批',
                                text: '审批',
                                classname: 'btn btn-xs btn-success btn-dialog',
                                icon: 'fa fa-check',
                                url: 'transfer/record/approve',
                                params: function(e, value, row, index) {
                                    return {ids: row.id};
                                },
                                visible: function(row) {
                                    return row.approval_status == 0;
                                },
                                success: function(data, ret){
                                    table.bootstrapTable('refresh');
                                    return false;
                                },
                                error: function(data, ret){
                                    Layer.alert(ret.msg);
                                    return false;
                                }
                            }]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        detail: function () {
            Controller.api.bindevent();
        },
        approve: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 