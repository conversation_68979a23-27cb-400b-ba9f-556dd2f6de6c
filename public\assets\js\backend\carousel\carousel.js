define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'carousel/carousel/index' + location.search,
                    add_url: 'carousel/carousel/add',
                    edit_url: 'carousel/carousel/edit',
                    del_url: 'carousel/carousel/del',
                    multi_url: 'carousel/carousel/multi',
                    table: 'esop_carousel_images',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'sort_order',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), operate: 'LIKE'},
                        {field: 'image_url', title: __('Image url'), formatter: Table.api.formatter.image, operate: false},
                        {field: 'title', title: __('Title'), operate: 'LIKE'},
                        {field: 'subtitle', title: __('Subtitle'), operate: 'LIKE'},
                        {field: 'link_url', title: __('Link url')},
                        {field: 'sort_order', title: __('Sort order')},
                        {field: 'status', title: __('Status'), formatter: Table.api.formatter.status, searchList: {0: __('Status 0'), 1: __('Status 1')}},
                        {field: 'created_at', title: __('Created at'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 