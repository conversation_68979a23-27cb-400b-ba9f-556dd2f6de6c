define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'stock/management/index' + location.search,
                    add_url: 'stock/management/add',
                    edit_url: 'stock/management/edit',
                    table: 'esop_stock_management',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'stock_code', title: __('Stock code'), operate: 'LIKE'},
                        {field: 'stock_name', title: __('Stock name'), operate: 'LIKE'},
                        {field: 'stock_unit', title: __('Stock unit'), operate: 'BETWEEN'},
                        {field: 'stock_price', title: __('Stock price'), operate: 'BETWEEN'},
                        {field: 'price_type_text', title: __('Price type'), operate: false},
                        {field: 'price_type', title: __('Price type'), visible: false, searchList: {"1": __('Average price'), "2": __('Agreement price')}},
                        {field: 'average_time', title: __('Average time')},
                        {field: 'exchangeable_amount', title: __('Exchangeable amount'), operate: 'BETWEEN'},
                        {field: 'is_on_shelf_text', title: __('Is on shelf'), operate: false},
                        {field: 'is_on_shelf', title: __('Is on shelf'), visible: false, searchList: {"0": __('Off shelf'), "1": __('On shelf')}},
                        {field: 'created_at', title: __('Created at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updated_at', title: __('Updated at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate, 
                            buttons: [
                                {
                                    name: '切换上架状态',
                                    text: __('切换上架状态'),
                                    title: __('切换上架状态'),
                                    icon: 'fa fa-toggle-on',
                                    classname: 'btn btn-xs btn-info btn-ajax',
                                    url: 'stock/management/toggle',
                                    confirm: __('确定要上架/下架吗？'),
                                    success: function (data, ret) {
                                        $(".btn-refresh").trigger("click");
                                    }
                                }
                            ]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            Controller.api.initFormEvents();
            // 触发一次切换，确保初始状态正确
            $('input[name="row[price_type]"]:checked').trigger('change');
        },
        edit: function () {
            Controller.api.bindevent();
            Controller.api.initFormEvents();

            // 编辑页初始化时根据价格类型进行相应处理
            var type = $('input[name="row[price_type]"]:checked').val();
            var stockCode = $('#c-stock_code').val();
            if(stockCode) {
                if(type == 1) {
                    // 如果是均价模式，获取股票信息
                    Controller.api.getStockInfo(stockCode);
                }
                // 约定价模式不需要自动获取，用户可以手动点击按钮
            }
            // 触发一次切换，确保初始状态正确
            $('input[name="row[price_type]"]:checked').trigger('change');
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            initFormEvents: function () {
                // 监听价格类型选择变化
                $('input[name="row[price_type]"]').on('change', function() {
                    var type = $('input[name="row[price_type]"]:checked').val();
                    if(type == 1) { // 均价
                        $('#average-time-group').show();
                        $('#stock-price-group').show();
                        $('#c-average_time').attr('data-rule', 'required');
                        $('#c-stock_price').attr('data-rule', 'required');

                        // 显示计算平均价格按钮，隐藏刷新最新价格按钮
                        $('#btn-calculate-average-price').show();
                        $('#btn-refresh-current-price').hide();
                        $('#price-help-text').text('点击按钮根据股票代码和平均天数自动计算平均价格');
                    } else { // 约定价
                        $('#average-time-group').hide();
                        $('#stock-price-group').show();
                        $('#c-average_time').removeAttr('data-rule');
                        $('#c-stock_price').attr('data-rule', 'required');

                        // 隐藏计算平均价格按钮，显示刷新最新价格按钮
                        $('#btn-calculate-average-price').hide();
                        $('#btn-refresh-current-price').show();
                        $('#price-help-text').text('请输入约定价格，或点击按钮获取当前最新价格参考');
                    }
                });

                // 绑定刷新按钮点击事件
                $('#btn-refresh-current-price').on('click', function() {
                    var type = $('input[name="row[price_type]"]:checked').val();
                    var stockCode = $('#c-stock_code').val();
                    if(type == 2 && stockCode) {
                        Controller.api.fetchLatestPrice(stockCode);
                    } else {
                        Toastr.warning('请选择约定价并输入股票代码');
                    }
                });

                // 绑定计算平均价格按钮点击事件
                $('#btn-calculate-average-price').on('click', function() {
                    var type = $('input[name="row[price_type]"]:checked').val();
                    var stockCode = $('#c-stock_code').val();
                    var averageTime = $('#c-average_time').val();

                    if(type != 1) {
                        Toastr.warning('请选择均价模式');
                        return;
                    }

                    if(!stockCode) {
                        Toastr.warning('请输入股票代码');
                        return;
                    }

                    if(!averageTime || averageTime <= 0) {
                        Toastr.warning('请输入有效的平均天数');
                        return;
                    }

                    if(averageTime > 120) {
                        Toastr.warning('平均天数不能超过120天');
                        return;
                    }

                    Controller.api.calculateAveragePrice(stockCode, averageTime);
                });

                // 监听股票代码输入变化，在均价模式下自动获取股票信息
                $('#c-stock_code').on('blur', function() {
                    var type = $('input[name="row[price_type]"]:checked').val();
                    var stockCode = $(this).val();
                    if(type == 1 && stockCode) {
                        Controller.api.getStockInfo(stockCode);
                    }
                });
            },
            // 新增：获取最新价格的AJAX方法
            fetchLatestPrice: function(stockCode) {
                // 显示加载状态
                var $btn = $('#btn-refresh-current-price');
                var originalText = $btn.text();
                $btn.text('获取中...').prop('disabled', true);

                // 调用后端接口获取最新价格
                $.ajax({
                    url: 'stock/management/get_latest_price',
                    type: 'GET',
                    dataType: 'json',
                    data: {stock_code: stockCode},
                    success: function(res) {
                        if(res && res.code === 1 && res.data && res.data.price) {
                            $('#c-stock_price').val(res.data.price);
                            Toastr.success('获取最新价格成功：' + res.data.price);
                        } else {
                            Toastr.error(res.msg || '获取最新价格失败');
                        }
                    },
                    error: function(xhr, status) {
                        var errorMsg = '获取最新价格失败';
                        if (xhr.responseJSON && xhr.responseJSON.msg) {
                            errorMsg = xhr.responseJSON.msg;
                        } else if (status === 'timeout') {
                            errorMsg = '请求超时，请重试';
                        } else if (status === 'error') {
                            errorMsg = '网络错误，请检查网络连接';
                        }
                        Toastr.error(errorMsg);
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $btn.text(originalText).prop('disabled', false);
                    }
                });
            },

            // 新增：计算平均价格的AJAX方法
            calculateAveragePrice: function(stockCode, days) {
                // 显示加载状态
                var $btn = $('#btn-calculate-average-price');
                var originalText = $btn.text();
                $btn.text('计算中...').prop('disabled', true);

                // 调用后端接口计算平均价格
                $.ajax({
                    url: 'stock/management/get_average_price',
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        stock_code: stockCode,
                        days: days,
                        price_type: 'close' // 使用收盘价计算
                    },
                    success: function(res) {
                        if(res && res.code === 1 && res.data && res.data.price) {
                            $('#c-stock_price').val(res.data.price);
                            Toastr.success('计算' + days + '天平均收盘价成功：' + res.data.price);
                        } else {
                            Toastr.error(res.msg || '计算平均价格失败');
                        }
                    },
                    error: function(xhr, status, error) {
                        var errorMsg = '计算平均价格失败';
                        if (xhr.responseJSON && xhr.responseJSON.msg) {
                            errorMsg = xhr.responseJSON.msg;
                        } else if (status === 'timeout') {
                            errorMsg = '请求超时，请重试';
                        } else if (status === 'error') {
                            errorMsg = '网络错误，请检查网络连接';
                        }
                        Toastr.error(errorMsg);
                    },
                    complete: function() {
                        // 恢复按钮状态
                        $btn.text(originalText).prop('disabled', false);
                    }
                });
            },

            // 新增：获取股票信息的AJAX方法
            getStockInfo: function(stockCode) {
                $.ajax({
                    url: 'stock/management/get_stock_info',
                    type: 'GET',
                    dataType: 'json',
                    data: {stock_code: stockCode},
                    success: function(res) {
                        if(res && res.code === 1 && res.data) {
                            var info = res.data;
                            var message = '股票 ' + stockCode + ' 数据信息：\n';
                            message += '可用数据天数：' + info.available_days + '天\n';
                            message += '最大平均天数：' + info.max_days + '天\n';
                            message += '推荐平均天数：' + info.recommended_days + '天';

                            // 更新平均天数输入框的推荐值
                            if ($('#c-average_time').val() === '' || $('#c-average_time').val() == 0) {
                                $('#c-average_time').val(info.recommended_days);
                            }

                            Toastr.info(message);
                        }
                    },
                    error: function() {
                        // 静默失败，不显示错误信息
                    }
                });
            }
        }
    };
    return Controller;
}); 