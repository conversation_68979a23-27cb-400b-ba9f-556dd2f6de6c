<?php

namespace app\admin\controller\finance;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 财务管理
 *
 * @icon fa fa-money
 */
class Finance extends Backend
{
    /**
     * 初始化
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->assignconfig('finance_active_tab', $this->request->action());
    }
    
    /**
     * 查看
     */
    public function index()
    {
        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());
        
        return $this->view->fetch();
    }
    
    /**
     * 获取资产统计数据
     */
    protected function getAssetStatistics()
    {
        // 一次性查询locked_assets、available_assets、pending_assets
        $assets = Db::name('esop_user_assets')->field([
            'SUM(locked_assets) as locked_assets',
            'SUM(available_assets) as available_assets',
            'SUM(pending_assets) as pending_assets'
        ])->find();
        $lockedAssets = (float)($assets['locked_assets'] ?? 0);
        $availableAssets = (float)($assets['available_assets'] ?? 0);
        $pendingAssets = (float)($assets['pending_assets'] ?? 0);

        $exerciseAssets = Db::name('esop_vesting_records')->where('vesting_status', 1)->sum('vesting_amount');

        // 计算股票资产
        $stockAssets = 0;
        // 查询所有用户的股票记录
        $stockRecords = Db::name('esop_user_stock_records')->select();
        if (!empty($stockRecords)) {
            // 提取所有股票ID
            $stockIds = [];
            foreach ($stockRecords as $sr) {
                $stockIds[] = $sr['stock_id'];
            }
            
            // 查询对应股票价格，返回键值对 [id => price]
            $priceMap = Db::name('esop_stock_management')->whereIn('id', $stockIds)->column('stock_price', 'id');
            // 计算股票资产：数量 * 当前价格 之和
            foreach ($stockRecords as $sr) {
                $price = isset($priceMap[$sr['stock_id']]) ? (float)$priceMap[$sr['stock_id']] : 0;
                $value = (float)$sr['amount'] * $price;
                $stockAssets += $value;
            }
        }

        // 计算总资产 = 可用资产 + 待解冻资产 + 股票资产 + 锁定资产
        $totalAssets = $availableAssets + $pendingAssets + $stockAssets + $lockedAssets;

        // 兼容旧视图，保留exchangeableAssets和exchangedAssets变量
        // exchangeableAssets等同于availableAssets，exchangedAssets等同于stockAssets
        $exchangeableAssets = $availableAssets;
        $exchangedAssets = $stockAssets;

        return [
            'totalAssets' => $totalAssets,
            'availableAssets' => $availableAssets,
            'pendingAssets' => $pendingAssets,
            'stockAssets' => $stockAssets,
            'exerciseAssets' => $exerciseAssets,
            'exchangeableAssets' => $exchangeableAssets, // 兼容旧视图
            'exchangedAssets' => $exchangedAssets, // 兼容旧视图
        ];
    }
    
    /**
     * 授权资产记录
     */
    public function grantAssets()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            
            $filterArr = $filter ? json_decode($filter, true) : [];
            
            // 获取分页数据
            $offset = $this->request->request('offset/d', 0);
            $limit = $this->request->request('limit/d', 10);
            $page = floor($offset / $limit) + 1; // 计算页码
            
            // 直接从EsopGrantStatistics模型读取数据，只查询会员记录
            $model = new \app\common\model\EsopGrantStatistics();
            $query = $model->alias('s')
                ->field([
                    's.id',
                    's.operator_id',
                    's.operator_name',
                    's.account_id',
                    's.account_name',
                    's.account_type',
                    's.grant_amount',
                    's.after_available_assets',
                    's.after_pending_assets',
                    's.created_at',
                    's.remark'
                ])
                ->where('s.account_type', 2); // 只查询C端用户记录（会员）
                
            // 添加关键词搜索
            if (!empty($filterArr['member'])) {
                $keyword = $filterArr['member'];
                // 直接用表中的字段搜索
                $query->where('s.account_name|s.operator_name', 'LIKE', "%{$keyword}%");
            }
            
            // 查询当前页数据
            $list = $query->order('s.id DESC')
                ->paginate($limit, false, [
                    'page' => $page
                ]);

            // 处理查询结果（只处理会员记录）
            $resultRows = [];
            
            // 批量查询B端账户信息
            $accountIds = array_column($list->items(), 'account_id');
            $operatorIds = array_column($list->items(), 'operator_id');
            $allUserIds = array_merge($accountIds, $operatorIds);
            $allUserIds = array_filter($allUserIds); // 过滤空值
            
            $bAccountMap = [];
            if (!empty($allUserIds)) {
                $bAccounts = \app\common\model\EsopBAccount::select();
                foreach ($bAccounts as $bAccount) {
                    $userIds = explode(',', $bAccount['user_ids']);
                    foreach ($userIds as $userId) {
                        if (in_array($userId, $allUserIds)) {
                            $bAccountMap[$userId] = $bAccount['account_name'];
                        }
                    }
                }
            }
            
            foreach ($list as $log) {
                $bAccountName = $bAccountMap[$log['account_id']] ?? '';
                $operatorBAccountName = $bAccountMap[$log['operator_id']] ?? '';
                
                $record = [
                    'id' => $log['id'],
                    'real_name' => $log['account_name'],
                    'account_name' => '', // 会员记录不需要显示B端账户信息
                    'operator_name' => $log['operator_name'],
                    'grant_amount' => $log['grant_amount'],
                    'after_amount' => $log['after_available_assets'] + $log['after_pending_assets'],
                    'created_at' => $log['created_at'],
                    'remark' => '会员资产授权',
                    'member_name' => $log['account_name'] . '（' . $operatorBAccountName . '）',
                    'formatted_time' => date('Y-m-d H:i:s', strtotime($log['created_at'])),
                    'record_type' => 'c'
                ];

                // 格式化金额显示 - 根据金额正负值决定颜色和符号
                if ($log['grant_amount'] > 0) {
                    $record['formatted_amount'] = '<span style="color:red">+' . number_format($log['grant_amount'], 2) . '</span>';
                } else {
                    $record['formatted_amount'] = '<span style="color:green">' . number_format($log['grant_amount'], 2) . '</span>';
                }

                // 添加到结果数组
                $resultRows[] = $record;
            }
            
            // 构建结果数据
            $result = [
                "total" => $list->total(),
                "rows" => $resultRows
            ];
            
            return json($result);
        }
        
        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());
        
        return $this->view->fetch();
    }
    
    /**
     * 待解禁记录
     */
    public function pendingAssets()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);

        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');

            $filterArr = $filter ? json_decode($filter, true) : [];
            $opArr = $op ? json_decode($op, true) : [];

            // 获取分页数据
            $offset = $this->request->request('offset/d', 0);
            $limit = $this->request->request('limit/d', 10);
            $page = floor($offset / $limit) + 1; // 计算页码

            // 查询每个用户的待解禁资产总和
            $query = Db::name('esop_user_assets')
                ->alias('ua')
                ->join('esop_user_profiles up', 'ua.user_id = up.user_id', 'LEFT')
                ->field([
                    'ua.user_id',
                    'up.real_name as member_name',
                    'SUM(ua.pending_assets) as pending_amount'
                ])
                ->where('ua.pending_assets', '>', 0)
                ->group('ua.user_id');

            // 添加搜索条件
            if (!empty($filterArr['member'])) {
                $keyword = $filterArr['member'];
                $query->where('up.real_name', 'LIKE', "%{$keyword}%");
            }

            // 查询当前页数据
            $list = $query->order('ua.user_id DESC')
                ->paginate($limit, false, [
                    'page' => $page
                ]);

            $userIds = [];
            foreach ($list->items() as $row) {
                $userIds[] = $row['user_id'];
            }

            // 查询每个用户的授权总额（grant_amount）
            $grantAmountMap = [];
            if (!empty($userIds)) {
                $grantList = Db::name('esop_grant_operation_logs')
                    ->where('target_user_id', 'IN', $userIds)
                    ->field(['target_user_id', 'SUM(grant_amount) as total_grant_amount'])
                    ->group('target_user_id')
                    ->select();
                foreach ($grantList as $g) {
                    $grantAmountMap[$g['target_user_id']] = $g['total_grant_amount'];
                }
            }

            // 优化：今天及之前所有未解禁金额合并算到今天，明天及以后的按最早一条显示
            $nextVestingMap = [];
            if (!empty($userIds)) {
                $today = date('Y-m-d');
                // 1. 查出所有今天及之前的未解禁记录
                $beforeOrToday = Db::name('esop_vesting_records')
                    ->where('vesting_status', 0)
                    ->where('target_user_id', 'IN', $userIds)
                    ->where('vesting_date', '<=', $today)
                    ->field('target_user_id, SUM(vesting_amount) as total_amount')
                    ->group('target_user_id')
                    ->select();

                $beforeOrTodayMap = [];
                foreach ($beforeOrToday as $row) {
                    $beforeOrTodayMap[$row['target_user_id']] = $row['total_amount'];
                }

                // 2. 查出所有明天及以后的未解禁记录
                $afterToday = Db::name('esop_vesting_records')
                    ->where('vesting_status', 0)
                    ->where('target_user_id', 'IN', $userIds)
                    ->where('vesting_date', '>', $today)
                    ->field('target_user_id, vesting_date, SUM(vesting_amount) as total_amount')
                    ->group('target_user_id, vesting_date')
                    ->order('vesting_date ASC')
                    ->select();

                $afterTodayMap = [];
                foreach ($afterToday as $row) {
                    $uid = $row['target_user_id'];
                    $date = $row['vesting_date'];
                    $afterTodayMap[$uid][$date] = $row['total_amount'];
                }

                // 3. 组装结果
                foreach ($userIds as $uid) {
                    if (!empty($beforeOrTodayMap[$uid])) {
                        // 有今天及之前的未解禁，全部算到今天
                        $nextVestingMap[$uid] = [
                            'vesting_date' => $today,
                            'vesting_amount' => $beforeOrTodayMap[$uid]
                        ];
                    } elseif (!empty($afterTodayMap[$uid])) {
                        // 没有今天及之前的，取最早的明天
                        $dates = array_keys($afterTodayMap[$uid]);
                        sort($dates);
                        $nextDate = $dates[0];
                        $nextVestingMap[$uid] = [
                            'vesting_date' => $nextDate,
                            'vesting_amount' => $afterTodayMap[$uid][$nextDate]
                        ];
                    } else {
                        // 没有任何未解禁
                        $nextVestingMap[$uid] = [
                            'vesting_date' => $today,
                            'vesting_amount' => 0
                        ];
                    }
                }
            }

            // 处理数据
            $resultRows = [];
            foreach ($list->items() as $row) {
                $displayName = $row['member_name'];
                $key = $row['user_id'];
                $nextVesting = isset($nextVestingMap[$key]) ? $nextVestingMap[$key] : null;
                $record = [
                    'user_id' => $row['user_id'],
                    'member_name' => $displayName,
                    'pending_amount' => number_format($row['pending_amount'], 2),
                    'grant_amount' => isset($grantAmountMap[$key]) ? number_format($grantAmountMap[$key], 2) : '0.00',
                    'next_vesting_date' => $nextVesting ? $nextVesting['vesting_date'] : '-',
                    'next_vesting_amount' => $nextVesting ? number_format($nextVesting['vesting_amount'], 2) : '-'
                ];
                $resultRows[] = $record;
            }

            // 构建结果数据
            $result = [
                "total" => $list->total(),
                "rows" => $resultRows
            ];

            return json($result);
        }

        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());

        return $this->view->fetch();
    }
    
    /**
     * 已解禁记录
     */
    public function vestedAssets()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            
            $filterArr = $filter ? json_decode($filter, true) : [];
            $opArr = $op ? json_decode($op, true) : [];
            
            // 获取分页数据
            $offset = $this->request->request('offset/d', 0);
            $limit = $this->request->request('limit/d', 10);
            $page = floor($offset / $limit) + 1; // 计算页码
            
            // 查询已解禁记录，按会员分组统计
            $query = Db::name('esop_vesting_records')
                ->alias('vr')
                ->join('esop_user_profiles up', 'vr.target_user_id = up.user_id', 'LEFT')
                ->join('esop_b_accounts ba', 'ba.id = (SELECT b_account_id FROM fa_esop_grant_operation_logs WHERE id = vr.grant_id LIMIT 1)', 'LEFT')
                ->join('(SELECT target_user_id, SUM(grant_amount) as total_grant_amount FROM fa_esop_grant_operation_logs GROUP BY target_user_id) g', 'g.target_user_id = vr.target_user_id', 'LEFT')
                ->field([
                    'vr.target_user_id as user_id',
                    'up.real_name as member_name',
                    'ba.account_name as b_account_name',
                    'SUM(vr.vesting_amount) as total_vesting_amount',
                    'IFNULL(g.total_grant_amount, 0) as total_grant_amount',
                    'MAX(vr.vesting_date) as last_vesting_date',
                    'COUNT(vr.id) as vesting_count'
                ])
                ->where('vr.vesting_status', 1) // 只查询已解禁的记录
                ->group('vr.target_user_id, up.real_name, ba.account_name');
                
            // 添加搜索条件
            if (!empty($filterArr['member'])) {
                $keyword = $filterArr['member'];
                $query->where('up.real_name|ba.account_name', 'LIKE', "%{$keyword}%");
            }
            
            if (!empty($filterArr['vesting_date']) && is_array($filterArr['vesting_date']) && count($filterArr['vesting_date']) == 2) {
                $query->where('vr.vesting_date', 'BETWEEN', $filterArr['vesting_date']);
            }
            
            // 查询当前页数据
            $list = $query->order('total_vesting_amount DESC')
                ->paginate($limit, false, [
                    'page' => $page
                ]);
                
            // 处理数据
            $resultRows = [];
            foreach ($list->items() as $row) {
                // 确定显示名称（优先显示会员名称，如果没有则显示B端账户名称）
                $displayName = $row['member_name'] ?: '未知用户';
                $accountType = $row['b_account_name'] ? "({$row['b_account_name']})" : '';
                
                $record = [
                    'id' => $row['user_id'],  // 使用用户ID作为记录ID
                    'user_id' => $row['user_id'],
                    'member_name' => $displayName ,
                    'vesting_amount' => number_format($row['total_vesting_amount'], 2),
                    'grant_amount' => number_format($row['total_grant_amount'], 2),
                    'vesting_date' => $row['last_vesting_date'],
                    'vesting_time' => $row['last_vesting_date'] . ' 00:00:00',
                    'vesting_count' => $row['vesting_count'],
                    'vesting_ratio' => $row['total_grant_amount'] > 0 ? 
                        number_format(($row['total_vesting_amount'] / $row['total_grant_amount']) * 100, 2) . '%' : '0%'
                ];
                
                $resultRows[] = $record;
            }
            
            // 构建结果数据
            $result = [
                "total" => $list->total(),
                "rows" => $resultRows
            ];
            
            return json($result);
        }
        
        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());
        
        return $this->view->fetch();
    }
    
    /**
     * 已换股记录
     */
    public function exchangedAssets()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            
            $filterArr = $filter ? json_decode($filter, true) : [];
            $opArr = $op ? json_decode($op, true) : [];
            
            // 获取分页数据
            $offset = $this->request->request('offset/d', 0);
            $limit = $this->request->request('limit/d', 10);
            $page = floor($offset / $limit) + 1; // 计算页码
            
            // 查询每个用户的换股汇总信息
            $query = Db::name('esop_exchange_records')
                ->alias('e')
                ->join('esop_user_profiles up', 'e.user_id = up.user_id', 'LEFT')
                ->field([
                    'e.user_id',
                    'up.real_name as member_name',
                    'COUNT(e.id) as exchange_count',
                    'SUM(e.amount) as total_exchange_amount',
                    'SUM(e.exchange_amount) as total_exchange_value',
                    'MAX(e.created_at) as last_exchange_time'
                ])
                ->group('e.user_id');

            // 添加搜索条件
            if (!empty($filterArr['member'])) {
                $keyword = $filterArr['member'];
                $query->where('up.real_name', 'LIKE', "%{$keyword}%");
            }

            if (!empty($filterArr['created_at']) && is_array($filterArr['created_at']) && count($filterArr['created_at']) == 2) {
                $query->where('e.created_at', 'BETWEEN', $filterArr['created_at']);
            }

            // 查询当前页数据
            $list = $query->order('e.user_id DESC')
                ->paginate($limit, false, [
                    'page' => $page
                ]);

            // 获取用户ID列表，用于查询详细的股票信息
            $userIds = [];
            foreach ($list->items() as $row) {
                $userIds[] = $row['user_id'];
            }

            // 查询每个用户的股票持有详情
            $stockDetailsMap = [];
            if (!empty($userIds)) {
                $stockDetails = Db::name('esop_user_stock_records')
                    ->alias('usr')
                    ->join('esop_stock_management sm', 'usr.stock_id = sm.id', 'LEFT')
                    ->where('usr.user_id', 'IN', $userIds)
                    ->where('usr.amount', '>', 0)
                    ->field([
                        'usr.user_id',
                        'GROUP_CONCAT(CONCAT(sm.stock_name, "(", usr.amount, ")") SEPARATOR ", ") as stock_details'
                    ])
                    ->group('usr.user_id')
                    ->select();

                foreach ($stockDetails as $detail) {
                    $stockDetailsMap[$detail['user_id']] = $detail['stock_details'];
                }
            }

            // 处理结果数据
            $resultRows = [];
            foreach ($list->items() as $row) {
                $row['stock_details'] = isset($stockDetailsMap[$row['user_id']]) ? $stockDetailsMap[$row['user_id']] : '无持股';
                $resultRows[] = $row;
            }

            // 构建结果数据
            $result = [
                "total" => $list->total(),
                "rows" => $resultRows
            ];
            
            return json($result);
        }
        
        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());
        
        return $this->view->fetch();
    }
    
    /**
     * 行权记录
     */
    public function exerciseAssets()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            
            $filterArr = $filter ? json_decode($filter, true) : [];
            $opArr = $op ? json_decode($op, true) : [];
            
            // 获取分页数据
            $offset = $this->request->request('offset/d', 0);
            $limit = $this->request->request('limit/d', 10);
            $page = floor($offset / $limit) + 1; // 计算页码
            
            // 查询每个用户的行权汇总信息
            $query = Db::name('esop_exercise_records')
                ->alias('e')
                ->join('esop_user_profiles up', 'e.user_id = up.user_id', 'LEFT')
                ->field([
                    'e.user_id',
                    'up.real_name as member_name',
                    'COUNT(e.id) as exercise_count',
                    'SUM(e.amount) as total_exercise_amount',
                    'SUM(e.exercise_amount) as total_exercise_value',
                    'SUM(e.exercise_fee) as total_exercise_fee',
                    'SUM(CASE WHEN e.approval_status = 0 THEN 1 ELSE 0 END) as pending_count',
                    'SUM(CASE WHEN e.approval_status = 1 THEN 1 ELSE 0 END) as approved_count',
                    'SUM(CASE WHEN e.approval_status = 2 THEN 1 ELSE 0 END) as rejected_count',
                    'MAX(e.created_at) as last_exercise_time'
                ])
                ->group('e.user_id');

            // 添加搜索条件
            if (!empty($filterArr['member'])) {
                $keyword = $filterArr['member'];
                $query->where('up.real_name', 'LIKE', "%{$keyword}%");
            }

            if (!empty($filterArr['created_at']) && is_array($filterArr['created_at']) && count($filterArr['created_at']) == 2) {
                $query->where('e.created_at', 'BETWEEN', $filterArr['created_at']);
            }

            // 查询总记录数和分页数据
            $list = $query->order('e.user_id DESC')
                ->paginate($limit, false, [
                    'page' => $page
                ]);

            // 获取用户ID列表，用于查询详细的行权股票信息
            $userIds = [];
            foreach ($list->items() as $row) {
                $userIds[] = $row['user_id'];
            }

            // 查询每个用户的行权股票详情
            $exerciseDetailsMap = [];
            if (!empty($userIds)) {
                $exerciseDetails = Db::name('esop_exercise_records')
                    ->alias('er')
                    ->join('esop_stock_management sm', 'er.stock_id = sm.id', 'LEFT')
                    ->where('er.user_id', 'IN', $userIds)
                    ->field([
                        'er.user_id',
                        'GROUP_CONCAT(DISTINCT CONCAT(sm.stock_name, "(", er.amount, ")") SEPARATOR ", ") as exercise_details'
                    ])
                    ->group('er.user_id')
                    ->select();

                foreach ($exerciseDetails as $detail) {
                    $exerciseDetailsMap[$detail['user_id']] = $detail['exercise_details'];
                }
            }

            // 处理结果数据
            $resultRows = [];
            foreach ($list->items() as $row) {
                // 添加行权详情
                $row['exercise_details'] = isset($exerciseDetailsMap[$row['user_id']]) ? $exerciseDetailsMap[$row['user_id']] : '无行权记录';

                // 处理审批状态汇总显示
                $statusSummary = [];
                if ($row['pending_count'] > 0) {
                    $statusSummary[] = '<span class="text-warning">待审批(' . $row['pending_count'] . ')</span>';
                }
                if ($row['approved_count'] > 0) {
                    $statusSummary[] = '<span class="text-success">已通过(' . $row['approved_count'] . ')</span>';
                }
                if ($row['rejected_count'] > 0) {
                    $statusSummary[] = '<span class="text-danger">已拒绝(' . $row['rejected_count'] . ')</span>';
                }
                $row['approval_status_summary'] = implode(' ', $statusSummary);

                $resultRows[] = $row;
            }

            // 构建结果数据
            $result = [
                "total" => $list->total(),
                "rows" => $resultRows
            ];
            
            return json($result);
        }
        
        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());
        
        return $this->view->fetch();
    }
    
    /**
     * 资产记录
     */
    public function assetLogs()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            $filter = $this->request->get("filter", '');
            $op = $this->request->get("op", '', 'trim');
            
            $filterArr = $filter ? json_decode($filter, true) : [];
            $opArr = $op ? json_decode($op, true) : [];
            
            // 获取分页数据
            $offset = $this->request->request('offset/d', 0);
            $limit = $this->request->request('limit/d', 10);
            $page = floor($offset / $limit) + 1; // 计算页码
            
            // 查询用户资金变化记录
            $query = Db::name('esop_user_fund_change_records')
                ->alias('f')
                ->join('esop_user_profiles up', 'f.user_id = up.user_id', 'LEFT')
                ->field([
                    'f.id',
                    'f.user_id',
                    'up.real_name as user_name',
                    'f.amount',
                    'f.action_type',
                    'f.before_available_assets',
                    'f.after_available_assets',
                    'f.before_pending_assets',
                    'f.after_pending_assets',
                    'f.remark',
                    'f.created_at'
                ]);
                
            // 添加搜索条件
            if (!empty($filterArr['user'])) {
                $keyword = $filterArr['user'];
                $query->join('esop_users u', 'f.user_id = u.id', 'LEFT')
                    ->where('up.real_name|up.nickname|u.phone', 'LIKE', "%{$keyword}%");
            }
            
            if (!empty($filterArr['action_type']) && is_numeric($filterArr['action_type'])) {
                $query->where('f.action_type', $filterArr['action_type']);
            }
            
            if (!empty($filterArr['created_at']) && is_array($filterArr['created_at']) && count($filterArr['created_at']) == 2) {
                $query->where('f.created_at', 'BETWEEN', $filterArr['created_at']);
            }
            
            // 查询总记录数和分页数据
            $list = $query->order('f.id DESC')
                ->paginate($limit, false, [
                    'page' => $page
                ]);
                
            $rows = $list->items();
                
            // 处理操作类型显示
            foreach ($rows as &$row) {
                // 计算总资产
                $row['before_total_assets'] = $row['before_available_assets'] + $row['before_pending_assets'];
                $row['after_total_assets'] = $row['after_available_assets'] + $row['after_pending_assets'];
                
                // 处理操作类型显示
                switch ($row['action_type']) {
                    case 1:
                        $row['action_type_text'] = '换股';
                        break;
                    case 2:
                        $row['action_type_text'] = '解禁';
                        break;
                    case 3:
                        $row['action_type_text'] = '转出';
                        break;
                    case 4:
                        $row['action_type_text'] = '接受';
                        break;
                    case 5:
                        $row['action_type_text'] = '授权';
                        break;
                    case 6:
                        $row['action_type_text'] = '锁定资金';
                        break;
                    case 7:
                        $row['action_type_text'] = '解锁资金';
                        break;
                }
                
                // 格式化金额显示
                if (in_array($row['action_type'], [2,4,5,7])) {
                    $row['formatted_amount'] = '<span style="color:red">+' . number_format($row['amount'], 2) . '</span>';
                } else {
                    $row['formatted_amount'] = '<span style="color:green">-' . number_format($row['amount'], 2) . '</span>';
                }
                
                // 格式化时间
                $row['formatted_time'] = date('Y-m-d H:i:s', strtotime($row['created_at']));
            }
            
            // 构建结果数据
            $result = [
                "total" => $list->total(),
                "rows" => $rows
            ];
            
            return json($result);
        }
        
        // 统计数据显示在页面顶部
        $this->view->assign($this->getAssetStatistics());
        
        return $this->view->fetch();
    }
}