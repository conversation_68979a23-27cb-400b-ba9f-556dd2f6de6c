理等功能
tgbot数字货币机器人
独立完成telegram bot开发，对接数字货币接口完成solana货币交易，用户管理，钱包，持仓的数据管理等
湖北两湖鲜达网络科技有限公司 全栈工程师
1.前期负责两湖鲜达 app 后端接口和管理端后端开发
2. 后期负责两湖鲜达 app 后端接口，管理端前后端的维护，以及日常运营人员功能开发需求
使用技术：
1.接口端使用 golang 进行开发，使用开源框架 beego，token 使用 jwt
 2. 管理端使用 php 开源框架 laraval
 3.定时任务使用 crontab
 4. 前端使用 vue+sass+webpack
北京蓝色星际科技股份有限公司 Golang
内容:
 1.负责建行安保系统的门禁和日常管理模块数据层维护和升级
2.负责建行安保系统应急管理模块的数据层 grpc 开发，表建立与维护
3. 负责建行安保系统应急管理模块的会议控制管理（入会，踢人，终端开溜，匿名入会）
使用技术：
1.web 层使用 gin 开源框架+rabbitmq
 2. 数据层使用 go-kits 微服务工具集
2022.07-2023.08
 2022.02-2022.07
业绩:
解决分布式 websocket 问题
2021.06-2022.02
武汉仕耀科技有限公司 PHP
内容:
 1.主要负责 ebay和 Shopee 跨界电商 erp 系统的开发与维护
2. 负责日常运营人员需求开发
3. 负责新 Walmart 新跨境电商平台的接入以及 erp 新模块开发
4. 解决 ebay和 shopee 跨境电商 erp 系统的历史 bug
使用技术：
1.使用 lavaral 框架进行开发
3.使用 jekins+docker 进行部署
2. 部分功能使用 golang（gin 框架）进行开发
业绩:
 1.解决大日志文件下载慢，且下载中断问题
2. 接入沃尔玛跨境电商平台到本地 erp 系统
北京迦游网络科技有限公司 PHP
内容:
 ● 配合运营人员完成相应个业务需求开发
● 开发维护7k7k网站管理的后台
● 改版和维护7k7k网站
● 改版和维护支付
● 进行日常网站活动开发
使用技术：
1.使用 ci 框架+mysql+redis
 2. 使用 jekins 进行项目部署
3. 使用 git 进行本版维护
4.部分定时任务使用 golang （如 sitemap 推送，缓存自动更新等）
业绩:
 1.改版7k7k主站
2. 接入第三方大型网页游戏
3. 开发抽奖活动
北京华数互动科技有限公司 PHP
内容:
 2020.05-2021.06
 2017.09-2020.05
主要是负责前端小程序和h5的接口，支付接口的编写和维护，所有公众号的编写和维护，独立负责星盘项目的开发，算法，
老项目的更新与维护
使用技术：
1.ci+mysql+redis
 2. Sphinx 全文索引引擎
3. smarty
 4. 微信小程序+微信公众号
业绩:
 1.微信小程序与公众号管理后台（管理微信小程序和公众号数据，自动回复等）
2. 文件缓存
3. 星座业务逻辑算法
2015.03-2017.05
北京融科汇通技术有限公司 PHP
全权负责PHP项目开发，主要项目是卫计委资金暂存管理系统
主要完成项目需求分析，项目设计，
代码编程，
主要与客户进行沟通负责解决各种业务问题和编码问题
项目经历
tgbot代币交易平台 全栈开发
2024.12-至今
tgBot项目主要是一个数字货币交易机器人。利用telegram这个即时通信软件提供的Bot api完成。交易货币主要是
solana。主要功能包括买卖，钱包管理，邀请分佣，语言切换，Gas费设置邓功能
skindeer饰品交易平台 全栈开发
2023.09-至今
skindeer是一个CSgo饰品交易平台。主要分为c端和b端。C端对接web和移动端。负责负责c端客户的饰品交易。b端主要
是对接开箱网或者CSgo饰品的大商，以及其他的饰品交易平台。货源主要是自有用户的货源，还有就是悠悠的货源以及c5
的货源。
两湖鲜达 全栈开发工程师
2022.06-至今
两湖鲜达 app 是一个农产品商城项目，前期主要负责后端接口开发和管理端后端开发，后期负责后端接口和管理端前后端
维护，运营新增功能开发。
使用技术：后端接口主要使用 golang 进心开发，管理端前端使用 vue 后端使用 php+laravel 框架进心开发，并使用了阿
里云短信服务和腾讯的 im 服务发送通知与短信。
服务器：所有的服务器都是用云服务器包括：mysql+redis 服务器和 ecs 开发服务器。
后端接口：主要负责支付，用户，权限，订单模块
管理端：主要负责用户管理和角色管理，订单管理
7k7k支付 php后端开发工程师
2020.05-至今
● 项目介绍：7k7k支付为主站和第三方大型网页游戏的提供游戏支付功能，分为平台支付和第三方游戏支付，提供市面上
包含的所有支付方式,以及第三方内部嵌套二维码方式，同时包括7k7k平台内部的游戏币充值
● 主要工作：对7k7k支付进行改版，提供支付宝和微信二码合一的支付方式，维护旧的支付项目代码
7k7k主站 php后端工程师
2020.05-至今
● 项目描述：7k7k主站，主要是提供国内外各种在线网页小游戏和大型网页游戏入口，提供各做类型如：策略，双人，女
生，塔防等在线pc和h5小游戏，游戏类别主要以flash和h5小游戏为主
● 主要工作：对7k7k主站的页面进行改版更新，对部分游戏板块比如易次元，橙光游戏进行改版更新
对7k7k主站后台管理的进行维护开发，配合产品和运营完成相应推广功能，编写脚本对网站缓存进行定时更新，sitemap推
送等
老黄历万年历维护 研发维护
项目属于上线很久的老项目，本项目主要实现吉日推算功能（搬家吉日、结婚节日等）
项目职责：
负责老黄历项目的改版更新、接口提供等维护工作
主要技术：
2017.11-至今
Php+h5+mysql
测测另一半 研发
2018.10-2018.10
内容:
项目是以星盘算法为基础进行开发的，主要就是通过获取客户的生日和出生地然后根据算法进行推演出另一半的相关星座信
息和恋爱属性呈现给用户 ,主要是小程序端和h5端
业绩:
项目完整交付
英文名起名 后端开发，出接口，算法
2018.09-2018.10
这个项目主要功能就是根据用户属于的一些个人属性信息进行算法推演出符合用户的英文名，属性包括姓名，星座，特性等
项目主要是HTML5和小程序端，我主要负责的任务是抓取国外英文名网站的数据，还有就是负责算法的开发，比如权重分的
分配等，还有就是出接口
公众号小程序管理系统 开发，设计
2018.05-2018.05
为了方便管理微信公众号和小程序的接口，和各种功能开发了这个内部管理系统，主要功能就是公众号的管理，token管
理，接口管理，公众号接口对接内部接口，菜单管理，小程序开关，小程序版本控制，广告开关等，项目开发为独立开发，
项目设计为和项目经理共同设计
体彩直播挂彩项目 后台开发工程师
2017.06-2017.09
项目概要：主要和河北体彩进行合作，开发的一款在线直播挂彩票的一个应用，主要是安卓和ios端，也提供网页端
开发角色：后台维护，日常体彩活动的开发
具体内容：维护后台代码，增加一些后台管理功能，主要有通知管理，活动管理，系统日志管理等。日常活动有辽宁挂彩满
1200送t-shirt一件，彩民抽奖活动等
阜阳人民医院大厅导航屏程序 开发工程师
2017.03-2017.04
项目概要：阜阳人民医院南区新开的园区，在每个楼层提供两台导航屏方便病人导航，主要使用PHP+前端知识做的一个导
航程序，包括楼层地图展示，和医生详情展示，药品价格与功能查询
开发人员：独立开发
工作内容：开发测试
阜南资金拨付暂存管理系统 开发工程师
2016.09-2017.01
 1.项目概况:卫计委会计部门与卫生院业务管理系统，主要用来简化卫计委和卫生院报帐，拨款，记账的业务
2.开发人员:独立二次开发，基于之前临泉县卫计委资金拨付暂存系统进行的二次开发
3.我的责任:主要负责项目业务沟通，程序编码，测试，问题解决
4.特别说明:所在公司是叔叔和别人合伙开的，我主要做的就是卫计委的系统，还做过颖泉县的，太和县的卫计委系统，大部
分一样就不再一一列举
办公OA（公司内部使用） 开发工程师
项目概要：公司内部使用主要用来报销，请假，项目管理的一个简易版oa系统
人员分工：独立开发
工作描述：程序编码，测试，问题解决
教育经历
湖北理工学院 大专 应用电子技术