<?php

namespace app\common\model;

use think\Model;

/**
 * 身份证图片模型
 */
class EsopIdCardImages extends Model
{
    // 表名
    protected $name = 'esop_id_card_images';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }
    
    /**
     * 关联用户资料模型
     */
    public function userProfile()
    {
        return $this->belongsTo('EsopUserProfile', 'user_id', 'user_id');
    }
    
    /**
     * 获取图片完整URL
     */
    public function getFrontImageAttr($value)
    {
        if (!$value) {
            return '';
        }
        
        if (strpos($value, 'http') !== 0) {
            return config('site.upload_url') . $value;
        }
        
        return $value;
    }
    
    /**
     * 获取图片完整URL
     */
    public function getBackImageAttr($value)
    {
        if (!$value) {
            return '';
        }
        
        if (strpos($value, 'http') !== 0) {
            return config('site.upload_url') . $value;
        }
        
        return $value;
    }
} 