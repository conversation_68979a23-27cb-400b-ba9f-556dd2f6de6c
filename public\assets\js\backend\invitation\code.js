define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'invitation/code/index' + location.search,
                    // add_url: 'invitation/code/add',
                    // edit_url: 'invitation/code/edit',
                    // del_url: 'invitation/code/del',
                    // multi_url: 'invitation/code/multi',
                    // generate_url: 'invitation/code/generate',
                    table: 'esop_invitation_codes',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID'), sortable: true},
                        {field: 'code', title: __('邀请码'), operate: 'LIKE'},
                        {field: 'member', title: __('会员'), operate: false},
                        // {field: 'is_used', title: __('使用状态'), searchList: {"0":__('未使用'),"1":__('已使用')}, formatter: Table.api.formatter.status},
                        {field: 'remark', title: __('备注'), operate: 'LIKE'},
                        {field: 'created_at', title: __('创建时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updated_at', title: __('更新时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        // {field: 'operate', title: __('操作'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 批量生成按钮事件
            $(document).on('click', '.btn-generate', function () {
                var options = {
                    shadeClose: false,
                    shade: [0.3, '#393D49'],
                    area: ['600px', '400px'],
                    callback: function (value) {
                        table.bootstrapTable('refresh');
                    }
                };
                Fast.api.open('invitation/code/generate', '批量生成邀请码', options);
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        generate: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 