<?php

namespace app\admin\validate;

use think\Validate;

/**
 * 股票管理验证器
 */
class EsopStockManagement extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'stock_code' => 'require|length:1,10',
        'stock_name' => 'require|length:1,50',
        'stock_price' => 'require|float|gt:0',
        'price_type' => 'require|in:1,2',
        'average_time' => 'require|integer|gt:0',
        'exchangeable_amount' => 'require|float|gt:0',
        'is_on_shelf' => 'require|in:0,1'
    ];

    /**
     * 提示消息
     */
    protected $message = [
        'stock_code.require' => '股票代码不能为空',
        'stock_code.length' => '股票代码长度必须在1-10个字符之间',
        'stock_name.require' => '股票名称不能为空',
        'stock_name.length' => '股票名称长度必须在1-50个字符之间',
        'stock_price.require' => '股票价格不能为空',
        'stock_price.float' => '股票价格必须为数字',
        'stock_price.gt' => '股票价格必须大于0',
        'price_type.require' => '价格类型不能为空',
        'price_type.in' => '价格类型必须为均价或约定价',
        'average_time.require' => '均价时间不能为空',
        'average_time.integer' => '均价时间必须为整数',
        'average_time.gt' => '均价时间必须大于0',
        'exchangeable_amount.require' => '可兑换数量不能为空',
        'exchangeable_amount.float' => '可兑换数量必须为数字',
        'exchangeable_amount.gt' => '可兑换数量必须大于0',
        'is_on_shelf.require' => '上架状态不能为空',
        'is_on_shelf.in' => '上架状态必须为上架或下架'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['stock_code', 'stock_name', 'stock_price', 'price_type', 'average_time', 'exchangeable_amount', 'is_on_shelf'],
        'edit' => ['stock_code', 'stock_name', 'stock_price', 'price_type', 'average_time', 'exchangeable_amount', 'is_on_shelf'],
    ];
} 