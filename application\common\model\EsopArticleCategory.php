<?php
namespace app\common\model;

use think\Model;

/**
 * 文章分类模型
 */
class EsopArticleCategory extends Model
{
    protected $name = 'esop_article_categories';

    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    /** 关联 B 端账户 */
    public function baccount()
    {
        return $this->belongsTo('EsopBAccount','b_account_id','id');
    }

    // 关联文章
    public function articles()
    {
        return $this->hasMany('EsopArticle','category_id','id');
    }
} 