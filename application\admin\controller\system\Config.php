<?php

namespace app\admin\controller\system;

use app\common\controller\Backend;
use app\common\model\EsopSystemConfig;
use app\common\model\EsopThirdPartyConfig;
use think\Db;
use think\Exception;

/**
 * 系统基础配置管理
 * 
 * @icon fa fa-cogs
 * @remark 系统基础配置，包括公司信息、LOGO、第三方接口等
 */
class Config extends Backend
{
    // 无需权限的方法
    protected $noNeedRight = ['index', 'uploadLogo', 'third', 'uploadApp'];
    
    // 系统配置模型
    protected $model = null;
    
    /**
     * 初始化
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopSystemConfig();
        
        // 确保JS配置正确加载，使用正确的路径
        $this->assignconfig('jsname', 'backend/system/config');
        
       
        // 不需要额外设置布局，使用默认布局
    }
    
    /**
     * 查看和更新系统配置
     */
    public function index()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }
            
            // 处理LOGO上传
            $logo = $this->request->file('logo');
            if ($logo) {
                $logoUrl = EsopSystemConfig::uploadLogo($logo);
                if ($logoUrl) {
                    $params['logo_url'] = $logoUrl;
                }
            }
            
            // 更新配置
            $result = EsopSystemConfig::updateConfig($params);
            if ($result) {
                $this->success('更新成功');
            } else {
                $this->error('更新失败');
            }
        }
        
        // 获取当前配置
        $config = EsopSystemConfig::getConfig();
        $this->view->assign("row", $config);
        
        // 获取第三方配置
        $third = $this->getThirdPartyConfigs();
        $this->view->assign('third', $third);
        
        return $this->view->fetch();
    }
    
    /**
     * 上传LOGO
     */
    public function uploadLogo()
    {
        // 获取表单上传文件
        $file = $this->request->file('file');
        if (!$file) {
            return json(['code' => 0, 'msg' => '没有文件上传']);
        }
        
        $logoUrl = EsopSystemConfig::uploadLogo($file);
        if ($logoUrl) {
            return json(['code' => 1, 'msg' => '上传成功', 'url' => $logoUrl]);
        } else {
            return json(['code' => 0, 'msg' => '上传失败']);
        }
    }

    /**
     * 上传APP包
     */
    public function uploadApp()
    {
        // 获取表单上传文件
        $file = $this->request->file('file');
        $appType = $this->request->post('app_type', ''); // android 或 ios

        if (!$file) {
            return json(['code' => 0, 'msg' => '没有文件上传']);
        }

        if (!in_array($appType, ['android', 'ios'])) {
            return json(['code' => 0, 'msg' => '应用类型参数错误']);
        }

        $appUrl = $this->uploadAppFile($file, $appType);
        if ($appUrl) {
            return json(['code' => 1, 'msg' => '上传成功', 'url' => $appUrl]);
        } else {
            return json(['code' => 0, 'msg' => '上传失败']);
        }
    }
    
    /**
     * 处理第三方配置
     */
    public function third()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("third/a");
            
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }
            
            try {
                Db::startTrans();
                
                // 处理SMS配置
                if (isset($params['sms'])) {
                    // 使用模型保存配置，自动处理时间戳
                    EsopThirdPartyConfig::saveConfigs($params['sms'], 'sms', 'aliyun');
                    
                    // 兼容旧数据 - 如果存在旧的templateCode，则更新为templateCode
                    $oldTemplate = EsopThirdPartyConfig::where([
                        'service_type' => 'sms',
                        'provider' => 'aliyun',
                        'config_key' => 'templateCode'
                    ])->find();
                    
                    if ($oldTemplate && !isset($params['sms']['templateCode'])) {
                        EsopThirdPartyConfig::create([
                            'service_type' => 'sms',
                            'provider' => 'aliyun',
                            'config_key' => 'templateCode',
                            'config_value' => $oldTemplate['config_value']
                        ]);
                    }
                }
                
                // 可以在这里处理其他第三方配置
                
                Db::commit();
                $this->success('更新成功');
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        
        return $this->error('请求方法错误');
    }
    
    /**
     * 获取第三方配置
     *
     * @return array
     */
    protected function getThirdPartyConfigs()
    {
        // 使用模型获取配置
        return EsopThirdPartyConfig::getConfigs();
    }

    /**
     * 上传APP文件
     *
     * @param object $file 上传文件对象
     * @param string $appType 应用类型 android|ios
     * @return string|false 成功返回文件路径，失败返回false
     */
    protected function uploadAppFile($file, $appType)
    {
        if (empty($file)) {
            return false;
        }

        // 根据应用类型设置允许的文件扩展名
        $allowedExtensions = [];
        if ($appType === 'android') {
            $allowedExtensions = ['apk'];
        } elseif ($appType === 'ios') {
            $allowedExtensions = ['ipa'];
        }

        // 获取文件后缀
        $suffix = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
        if (!in_array($suffix, $allowedExtensions)) {
            return false;
        }

        // 设置上传目录
        $uploadDir = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'apps' . DS . $appType;

        // 检查上传目录
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // 生成文件名
        $fileName = $appType . '_app_' . date('YmdHis') . rand(1000, 9999) . '.' . $suffix;

        // 保存文件
        $info = $file->move($uploadDir, $fileName);

        if ($info) {
            // 返回完整的文件访问URL（包含域名）
            $relativePath = '/uploads/apps/' . $appType . '/' . $fileName;
            $fullUrl = $this->getFullUrl($relativePath);
            return $fullUrl;
        } else {
            return false;
        }
    }

    /**
     * 获取完整URL（包含域名）
     *
     * @param string $relativePath 相对路径
     * @return string 完整URL
     */
    protected function getFullUrl($relativePath)
    {
        // 获取当前请求的协议
        $protocol = $this->request->isSsl() ? 'https://' : 'http://';

        // 获取当前请求的主机名
        $host = $this->request->host();

        // 组合完整URL
        $fullUrl = $protocol . $host . $relativePath;

        return $fullUrl;
    }
}
