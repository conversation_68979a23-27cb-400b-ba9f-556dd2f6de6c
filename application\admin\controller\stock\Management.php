<?php

namespace app\admin\controller\stock;

use app\common\controller\Backend;
use app\common\model\EsopStockPriceLog;
use think\Db;
use think\exception\ValidateException;
use think\exception\PDOException;
use Exception;

/**
 * 股票管理
 *
 * @icon fa fa-line-chart
 */
class Management extends Backend
{
    /**
     * 股票管理模型对象
     * @var \app\common\model\EsopStockManagement
     */
    protected $model = null;
    protected $searchFields = 'stock_code,stock_name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\EsopStockManagement;
    }

    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                // 验证股票代码唯一性
                if ($this->model->where('stock_code', $params['stock_code'])->find()) {
                    $this->error(__('Stock code already exists'));
                }

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    // 是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 验证股票代码唯一性（排除自己）
                if ($this->model->where('stock_code', $params['stock_code'])->where('id', '<>', $ids)->find()) {
                    $this->error(__('Stock code already exists'));
                }

                $result = false;
                Db::startTrans();
                try {
                    // 是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    
                    // 如果价格发生变化，记录价格修改日志
                    if ($row['stock_price'] != $params['stock_price']) {
                        $priceLog = new EsopStockPriceLog();
                        $priceLog->save([
                            'stock_id' => $ids,
                            'price_type' => $params['price_type'],
                            'old_price' => $row['stock_price'],
                            'new_price' => $params['stock_price'],
                            'set_by' => $this->auth->id,
                            'set_time' => date('Y-m-d H:i:s')
                        ]);
                    }
                    
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 获取价格历史记录
        $priceLogs = EsopStockPriceLog::with(['admin'])
            ->where('stock_id', $ids)
            ->order('created_at', 'desc')
            ->select();
            
        $this->view->assign("row", $row);
        $this->view->assign("priceLogs", $priceLogs);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();

            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $k => $v) {
                    $count += $v->delete();
                }
                Db::commit();
            } catch (PDOException $e) {
                Db::rollback();
                $this->error($e->getMessage());
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }

    /**
     * 切换上架状态
     */
    public function toggle($ids = "")
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        
        $value = $row->is_on_shelf == 1 ? 0 : 1;
        $row->is_on_shelf = $value;
        $row->save();
        
        $this->success();
    }

    /**
     * 获取指定股票代码的最新价格（AJAX接口）
     * @return \think\Response
     */
    public function get_latest_price()
    {
        // 仅允许AJAX请求
        if (!$this->request->isAjax()) {
            $this->error('非法请求');
        }
        // 获取股票代码参数
        $stockCode = $this->request->get('stock_code');
        if (!$stockCode) {
            $this->error('缺少股票代码参数');
        }
        // 调用Stock库获取最新价格
        $price = \app\common\library\Stock::getLatestStockPrice($stockCode);
        if ($price === null) {
            $this->error('获取最新价格失败');
        }
        // 返回fastadmin风格的json
        $this->success('获取成功', null, ['price' => $price]);
    }

    /**
     * 获取指定股票代码的平均价格（AJAX接口）
     * @return \think\Response
     */
    public function get_average_price()
    {
        // 仅允许AJAX请求
        if (!$this->request->isAjax()) {
            $this->error('非法请求');
        }

        // 获取参数
        $stockCode = $this->request->get('stock_code');
        $days = $this->request->get('days', 5); // 默认5天
        $priceType = $this->request->get('price_type', 'close'); // 默认收盘价

        if (!$stockCode) {
            $this->error('缺少股票代码参数');
        }

        if (!is_numeric($days) || $days <= 0) {
            $this->error('天数参数无效');
        }

        // 检查天数限制
        $maxDays = \app\common\library\Stock::getMaxAverageDays();
        if ($days > $maxDays) {
            $this->error("天数不能超过{$maxDays}天");
        }

        // 调用Stock库获取平均价格
        $averagePrice = \app\common\library\Stock::getAveragePrice($stockCode, intval($days), $priceType);
        if ($averagePrice === null) {
            $this->error('获取平均价格失败，请检查股票代码、天数设置或网络连接');
        }

        // 返回fastadmin风格的json
        $this->success('获取成功', null, [
            'price' => $averagePrice,
            'days' => $days,
            'price_type' => $priceType
        ]);
    }

    /**
     * 获取股票数据信息（AJAX接口）
     * @return \think\Response
     */
    public function get_stock_info()
    {
        // 仅允许AJAX请求
        if (!$this->request->isAjax()) {
            $this->error('非法请求');
        }

        // 获取股票代码参数
        $stockCode = $this->request->get('stock_code');
        if (!$stockCode) {
            $this->error('缺少股票代码参数');
        }

        // 获取可用数据天数
        $availableDays = \app\common\library\Stock::getAvailableDataDays($stockCode);
        $maxDays = \app\common\library\Stock::getMaxAverageDays();

        if ($availableDays === null) {
            $this->error('获取股票数据失败，请检查股票代码');
        }

        // 返回fastadmin风格的json
        $this->success('获取成功', null, [
            'available_days' => $availableDays,
            'max_days' => $maxDays,
            'recommended_days' => min($availableDays, $maxDays, 30) // 推荐天数，最多30天
        ]);
    }
} 