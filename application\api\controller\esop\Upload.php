<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\library\Upload as UploadLibrary;

/**
 * ESOP文件上传接口
 */
class Upload extends Api
{
    protected $noNeedLogin = ['image','idCardFront','idCardBack'];
    protected $noNeedRight = '*';
    
    /**
     * 上传图片接口
     *
     * @ApiMethod (POST)
     * @param File $file 文件流
     * @ApiReturn {"code":1,"msg":"","data":{"url":"\/uploads\/20230101\/a1b2c3d4.jpg"}}
     */
    public function image()
    {
        $file = $this->request->file('file');
        if (!$file) {
            return $this->toError(__('No file upload or server upload limit exceeded'));
        }
        
        // 配置上传参数
        $config = [
            'mimetype' => 'image/jpeg,image/png,image/gif,image/jpg,image/bmp',
            'ext'      => 'jpg,jpeg,png,gif,bmp',
            'maxsize'  => 10485760, // 10MB
        ];
        
        try {
            // 创建上传实例
            $upload = new UploadLibrary();
            // 设置文件
            $upload->setFile($file);
            
            // 获取文件后缀
            $fileInfo = $file->getInfo();
            $suffix = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));
            
            // 设置保存路径和文件名
            $savekey = 'id_card_' . $this->auth->id . '_' . uniqid() . '.' . $suffix;
            $savekey = '/uploads/' . date('Ymd') . '/' . $savekey;
            
            // 执行上传
            $info = $upload->upload($savekey);
            return $this->jsonSucess(__('Upload successful'), ['url' => $info['url']]);
        } catch (\Exception $e) {
            return $this->toError($e->getMessage());
        }
    }
    
    /**
     * 上传身份证正面图片
     */
    public function idCardFront()
    {
        $file = $this->request->file('file');
        if (!$file) {
            return $this->toError(__('No file upload or server upload limit exceeded'));
        }
        
        // 配置上传参数
        $config = [
            'mimetype' => 'image/jpeg,image/png,image/jpg',
            'ext'      => 'jpg,jpeg,png',
            'maxsize'  => 5242880, // 5MB
        ];
        
        try {
            // 创建上传实例
            $upload = new UploadLibrary();
            // 设置文件
            $upload->setFile($file);
            
            // 获取文件后缀
            $fileInfo = $file->getInfo();
            $suffix = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));
            
            // 设置保存路径和文件名
            $savekey = 'id_card_front_' . $this->auth->id . '_' . date('YmdHis') . '.' . $suffix;
            $savekey = '/uploads/' . date('Ymd') . '/' . $savekey;
            
            // 执行上传
            $info = $upload->upload($savekey);
            return $this->jsonSucess(__('Upload successful'), [
                'url' => $info['url'],
                'type' => 'front'
            ]);
        } catch (\Exception $e) {
            return $this->toError($e->getMessage());
        }
    }
    
    /**
     * 上传身份证背面图片
     */
    public function idCardBack()
    {
        $file = $this->request->file('file');
        if (!$file) {
            return $this->toError(__('No file upload or server upload limit exceeded'));
        }
        
        // 配置上传参数
        $config = [
            'mimetype' => 'image/jpeg,image/png,image/jpg',
            'ext'      => 'jpg,jpeg,png',
            'maxsize'  => 5242880, // 5MB
        ];
        
        try {
            // 创建上传实例
            $upload = new UploadLibrary();
            // 设置文件
            $upload->setFile($file);
            
            // 获取文件后缀
            $fileInfo = $file->getInfo();
            $suffix = strtolower(pathinfo($fileInfo['name'], PATHINFO_EXTENSION));
            
            // 设置保存路径和文件名
            $savekey = 'id_card_back_' . $this->auth->id . '_' . date('YmdHis') . '.' . $suffix;
            $savekey = '/uploads/' . date('Ymd') . '/' . $savekey;
            
            // 执行上传
            $info = $upload->upload($savekey);
            return $this->jsonSucess(__('Upload successful'), [
                'url' => $info['url'],
                'type' => 'back'
            ]);
        } catch (\Exception $e) {
            return $this->toError($e->getMessage());
        }
    }
} 