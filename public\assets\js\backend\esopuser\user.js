define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'esopuser/user/index' + location.search,
                    add_url: 'esopuser/user/add',
                    edit_url: 'esopuser/user/edit',
                    detail_url: 'esopuser/user/detail',
                    multi_url: 'esopuser/user/multi',
                    finance_detail_url: 'esopuser/user/finance_detail',
                    table: 'esop_users',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('ID')},
                        {field: 'phone', title: __('手机号'), operate: 'LIKE'},
                        {field: 'nickname', title: __('昵称'), operate: 'LIKE'},
                        {field: 'real_name', title: __('真实姓名'), operate: 'LIKE'},
                        {field: 'gender_text', title: __('性别'), searchList: {"0":__('保密'),"1":__('男'),"2":__('女')}, formatter: Table.api.formatter.normal, operate: false},
                        // 移除身份证字段显示
                        // {field: 'id_card_no', title: __('身份证号'), operate: 'LIKE'},
                        {field: 'pending_assets', title: __('待解仓'), operate: false, formatter: function(value, row, index){
                            return value + ' 元';
                        }},
                        {field: 'total_assets', title: __('总资产'), operate: false, formatter: function(value, row, index){
                            return value + ' 元';
                        }},
                        {field: 'available_assets', title: __('可换股'), operate: false, formatter: function(value, row, index){
                            return value + ' 元';
                        }},
                        {field: 'stock_details', title: __('股票'), operate: false, formatter: function(value, row, index){
                            return value || '无';
                        }},
                        {field: 'inviter_info', title: __('推荐人'), operate: false, formatter: function(value, row, index){
                            return value || '无';
                        }},
                        {field: 'audit_status_text', title: __('审核状态'), searchList: {"0":__('待审核'),"1":__('审核通过'),"2":__('审核不通过')}, formatter: function(value, row, index){
                            var statusMap = {
                                [__('待审核')]: 'info',
                                [__('审核通过')]: 'success',
                                [__('审核不通过')]: 'danger'
                            };
                            return '<span class="label label-' + statusMap[value] + '">' + value + '</span>';
                        }, operate: false},
                        {field: 'audit_status', title: __('审核状态'), searchList: {"0":__('待审核'),"1":__('审核通过'),"2":__('审核不通过')}, visible: false},
                        {field: 'status', title: __('状态值'), searchList: {"normal":__('正常'),"hidden":__('禁用')}, formatter: Table.api.formatter.status, visible: false},
                        {field: 'status_text', title: __('状态'), operate: false, formatter: function (value) {
                            var colorMap = {
                                '正常': 'success',
                                '禁用': 'danger'
                            };
                            var color = colorMap[value] || 'info';
                            return '<span class="label label-' + color + '">' + value + '</span>';
                        }},
                        {field: 'created_at', title: __('注册时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'last_login_time', title: __('最后登录时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                        {field: 'operate', title: __('操作'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    title: __('查看'),
                                    classname: 'btn btn-xs btn-primary btn-dialog',
                                    icon: 'fa fa-list',
                                    url: 'esopuser/user/detail',
                                    text: __('查看'),
                                    extend: 'data-area=\'["95%","90%"]\'',
                                },
                                {
                                    name: 'audit',
                                    title: __('审核认证'),
                                    classname: 'btn btn-xs btn-success btn-dialog',
                                    icon: 'fa fa-check-circle',
                                    url: 'esopuser/user/audit',
                                    text: __('审核'),
                                    visible: function(row) {
                                        return row.audit_status == 0;
                                    }
                                },
                                {
                                    name: 'finance_detail',
                                    title: __('资金详情'),
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-money',
                                    url: 'esopuser/user/finance_detail',
                                    text: __('资金详情'),
                                    extend: 'data-area=\'["95%","90%"]\'',
                                    visible: function(row) {
                                        return row.audit_status == 1;  // 只有审核通过的用户才显示资金详情按钮
                                    }
                                }
                            ]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        detail: function () {
            Controller.api.bindevent();
        },
        audit: function () {
            Controller.api.bindevent();
        },
        finance_detail: function () {
            // 绑定表单事件（如果有）
            Controller.api.bindevent();

            // 获取 URL 参数中的用户ID
            var ids = Fast.api.query('ids') || '';

            // 初始化表格参数
            Table.api.init({
                extend: {
                    index_url: 'esopuser/user/finance_detail?ids=' + ids,
                }
            });

            var table = $("#stock-table");

            // 初始化股票持有情况表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                pagination: true,
                sidePagination: 'server',
                columns: [
                    [
                        {field: 'stock_name', title: __('股票名称'), operate: false},
                        {field: 'stock_code', title: __('股票代码'), operate: false},
                        {field: 'amount', title: __('持有数量'), operate: false},
                        {field: 'updated_at', title: __('最后更新时间'), operate: false, formatter: Table.api.formatter.datetime},
                    ]
                ]
            });

            // 绑定事件
            Table.api.bindevent(table);
        },
        resetpwd: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 