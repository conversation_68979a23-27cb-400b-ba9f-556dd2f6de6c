<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopArticle;
use app\common\model\EsopBAccount;
use app\common\model\EsopNoticeApprovalRecord;
use app\common\model\EsopInvitationRelation;

/**
 * 平台公告接口
 */
class Notice extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = [];
    // 无需鉴权的接口
    protected $noNeedRight = '*';

    protected $board_id = 1;

    /**
     * 获取当前用户所属 B 端账户 ID
     * 如果用户为 B 端管理员，则直接返回其管理的 B 端账户；
     * 如果用户为被邀请者，则返回邀请关系中对应的 B 端账户；
     * 若均不存在，则返回 null。
     * @return int|null
     */
    protected function getUserBAccountId(): ?int
    {
        // 尝试获取当前已登录用户
        $user = $this->auth->getUser();
        if (!$user) {
            return null;
        }

        // 1. 判断当前用户是否为 B 端管理员
        $bAccountId = EsopBAccount::whereRaw("FIND_IN_SET({$user->id}, user_ids)")
            ->value('id');
        if ($bAccountId) {
            return (int)$bAccountId;
        }

        // 2. 判断当前用户是否为邀请关系中的成员
        $bAccountId = EsopInvitationRelation::where('invitee_id', $user->id)
            ->value('b_account_id');
        return $bAccountId ? (int)$bAccountId : null;
    }

    /**
     * 判断公告是否已审批通过
     * @param int $noticeId
     * @return bool
     */
    protected function isApproved(int $noticeId): bool
    {
        return EsopNoticeApprovalRecord::where('notice_id', $noticeId)
            ->where('status', 1)
            ->count() > 0;
    }

    /**
     * 获取公告审批状态
     * 0 = 待审批, 1 = 已通过, 2 = 未通过, null = 无记录
     * @param int $noticeId
     * @return int|null
     */
    protected function getApprovalStatus(int $noticeId): ?int
    {
        return EsopNoticeApprovalRecord::where('notice_id', $noticeId)
            ->value('status');
    }

    /**
     * 获取平台公告列表
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiParams (name="keyword", type="string", required=false, description="搜索关键词，可按标题/内容模糊匹配")
     * @ApiReturn  {"code":1,"msg":"","data":{"total":15,"per_page":10,"current_page":1,"last_page":2,"list":[{"id":1,"title":"公告标题","summary":"摘要","image":"图片URL","created_at":"2025-06-27 15:32:11","is_read":0}]}}
     */
    public function index()
    {
        // ------------------------------
        // 读取分页参数，默认 page=1, limit=10
        // ------------------------------
        $page     = $this->request->param('page/d', 1);
        $limit    = $this->request->param('limit/d', 10);
        $keyword  = $this->request->param('keyword/s', '');

        // 公告中心 board_id=1，status=1 为已发布且已审批通过
        // 仅返回平台通用公告 (b_account_id 为空或 0) 以及用户所属 B 端公告
        $userBAccountId = $this->getUserBAccountId();

        // 获取当前用户已读公告 ID 列表，用来标记是否已读
        $user = $this->auth->getUser();
        $readIds = [];
        if ($user) {
            $readIds = \app\common\model\EsopNoticeReadRecord::getReadIds($user->id);
        }

        // 构建查询
        $query = EsopArticle::alias('a')
            ->join('esop_notice_approval_records ar', 'a.id = ar.notice_id')
            ->where('ar.status', 1)
            ->where('a.board_id', $this->board_id)
            ->where('a.status', 1)
            ->where(function ($query) use ($userBAccountId) {
                // 平台通用公告
                $query->whereNull('a.b_account_id')->whereOr('a.b_account_id', 0);
                // B 端专属公告
                if ($userBAccountId) {
                    $query->whereOr('a.b_account_id', $userBAccountId);
                }
            })
            ->field('a.*')
            ->order('a.created_at', 'desc');

        // 若传入 keyword，则按标题/内容模糊搜索
        if ($keyword !== '') {
            $query->where(function($q) use ($keyword) {
                $q->whereOr('a.title', 'like', "%{$keyword}%")
                  ->whereOr('a.content', 'like', "%{$keyword}%");
            });
        }

        // 使用 paginate 进行分页查询
        $list = $query->paginate($limit);

        // 整理返回数据
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id'         => $item->id,
                'title'      => $item->title,
                'summary'    => $item->summary ?? '',           // 摘要字段，若无则为空
                'image'      => $item->cover_image ?? '',       // 封面图字段，若无则为空
                'created_at' => $item->created_at,
                'is_read'    => in_array($item->id, $readIds) ? 1 : 0, // 0=未读,1=已读
                'is_b_user'  => $item->b_account_id ? 1 : 0, // 0=否,1=是
            ];
        }

        // 组装分页结构
        $result = [
            'total'        => $list->total(),      // 总记录数
            'per_page'     => $limit,              // 每页数量
            'current_page' => $page,               // 当前页码
            'last_page'    => $list->lastPage(),   // 最后一页页码
            'list'         => $data,               // 数据列表
        ];

        // 返回结果
        $this->success('获取成功', $result);
    }

    /**
     * 获取平台公告详情
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="公告ID")
     * @ApiReturn  {"code":1,"msg":"","data":{"id":1,"title":"公告标题","content":"公告内容","image":"图片URL","created_at":"2025-06-27 15:32:11"}}
     */
    public function detail()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error('参数错误');
        }

        // 查询公告详情，同时校验用户权限
        $notice = EsopArticle::where('id', $id)
            ->where('board_id', $this->board_id)
            ->where('status', 1)
            ->find();
        if (!$notice) {
            $this->error('公告不存在');
        }
        
        $approvalStatus = $this->getApprovalStatus($notice->id);

        if ($approvalStatus !== 1) {
            // 根据不同状态返回不同提示
            if ($approvalStatus === 2) {
                $this->error('公告审批未通过');
            } else {
                $this->error('公告正在审批中');
            }
        }

        // 权限校验：若该公告属于某个 B 端，只允许其团队成员访问
        if ($notice->b_account_id) {
            $userBAccountId = $this->getUserBAccountId();
            if ($userBAccountId !== (int)$notice->b_account_id) {
                $this->error('无权限查看该公告');
            }
        }

        $data = [
            'id'         => $notice->id,
            'title'      => $notice->title,
            'content'    => $notice->content,
            'image'      => $notice->cover_image ?? '',
            'created_at' => $notice->created_at,
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 搜索平台公告
     * @ApiMethod (GET)
     * @ApiParams (name="keyword", type="string", required=true, description="搜索关键词")
     * @ApiParams (name="page", type="integer", required=false, description="页码，默认1")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量，默认10")
     * @ApiReturn  {"code":1,"msg":"","data":{"total":15,"per_page":10,"current_page":1,"last_page":2,"data":[{"id":1,"title":"公告标题","summary":"摘要","image":"图片URL","created_at":"2025-06-27 15:32:11"}]}}
     */
    public function search()
    {
        // 获取参数
        $keyword = $this->request->param('keyword/s', '');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);
        
        if (empty($keyword)) {
            $this->error('请输入搜索关键词');
        }
        
        // 查询条件：已发布 + 已审批通过
        $userBAccountId = $this->getUserBAccountId();
        $query = EsopArticle::alias('a')
            ->join('esop_notice_approval_records ar', 'a.id = ar.notice_id')
            ->where('ar.status', 1)
            ->where('a.board_id', $this->board_id)
            ->where('a.status', 1)
            ->where(function($query) use ($keyword) {
                $query->whereOr('a.title', 'like', "%{$keyword}%")
                      ->whereOr('a.content', 'like', "%{$keyword}%");
            })
            ->where(function ($query) use ($userBAccountId) {
                $query->whereNull('a.b_account_id')->whereOr('a.b_account_id', 0);
                if ($userBAccountId) {
                    $query->whereOr('a.b_account_id', $userBAccountId);
                }
            })
            ->field('a.*');
        
        // 使用FastAdmin框架标准方式，直接使用paginate方法
        $list = $query->order('a.created_at', 'desc')
            ->paginate($limit);
        
        $data = [];
        foreach ($list as $item) {
            $data[] = [
                'id'         => $item->id,
                'title'      => $item->title,
                'summary'    => $item->summary ?? '', // 摘要字段，若无则为空
                'image'      => $item->image ?? '',   // 图片字段，若无则为空
                'created_at' => $item->created_at,
            ];
        }
        
        // 返回结果
        $result = [
            'total' => $list->total(), // 使用paginate返回对象的total()方法获取总数
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => $list->lastPage(), // 使用lastPage()获取最后一页页码
            'list' => $data
        ];
        $this->success('搜索成功', $result);
    }
    
    /**
     * 记录用户已读公告
     * @ApiMethod (POST)
     * @ApiParams (name="notice_id", type="integer", required=true, description="公告ID")
     * @ApiReturn  {"code":1,"msg":"记录成功","data":[]}
     */
    public function read()
    {
        // 需要登录
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }
        
        // 获取参数
        $params = $this->request->param(true);
        $noticeId = isset($params['notice_id']) ? intval($params['notice_id']) : 0;
        
        if (empty($noticeId)) {
            $this->error('公告ID不能为空');
        }
        
        // 检查公告是否存在
        $notice = \app\common\model\EsopArticle::where('id', $noticeId)
            ->where('board_id', $this->board_id)
            ->where('status', 1)
            ->find();
            
        if (!$notice) {
            $this->error('公告不存在');
        }
        
        // 添加已读记录
        $result = \app\common\model\EsopNoticeReadRecord::addRecord($user->id, $noticeId);
        
        if ($result) {
            $this->success('记录成功');
        } else {
            $this->error('记录失败');
        }
    }
    
    /**
     * 获取用户未读公告数量
     * @ApiMethod (GET)
     * @ApiReturn  {"code":1,"msg":"获取成功","data":{"unread_count":5}}
     */
    public function unread()
    {
        // 需要登录
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }

        // 获取用户已读公告ID列表
        // 调用模型方法获取已读公告ID数组
        $readIds = \app\common\model\EsopNoticeReadRecord::getReadIds($user->id);

        // 构建未读公告查询，限定用户所属 B 端公告
        $userBAccountId = $this->getUserBAccountId();
        $query = \app\common\model\EsopArticle::alias('a')
            ->join('esop_notice_approval_records ar', 'a.id = ar.notice_id')
            ->where('ar.status', 1)
            ->where('a.board_id', $this->board_id)
            ->where('a.status', 1)
            ->where(function ($query) use ($userBAccountId) {
                $query->whereNull('a.b_account_id')->whereOr('a.b_account_id', 0);
                if ($userBAccountId) {
                    $query->whereOr('a.b_account_id', $userBAccountId);
                }
            });

        // 如果有已读ID，则排除
        if (!empty($readIds)) {
            $query->whereNotIn('a.id', $readIds);
        }

        // 统计未读公告数量
        $unreadCount = $query->count();

        // 返回未读数量，结构为 {"unread_count": 数量}
        $this->success('获取成功', ['unread_count' => $unreadCount]);
    }

    /**
     * B端用户发布公告接口
     *
     * @ApiMethod (POST)
     * @ApiSummary  B端发布公告
     * @ApiParams   (name="title", type="string", required=true, description="公告标题")
     * @ApiParams   (name="content", type="string", required=true, description="公告内容")
     * @ApiParams   (name="image", type="string", required=false, description="封面图URL，可选")
     * @ApiReturn   {"code":1,"msg":"发布成功","data":{"id":1}}
     */
    public function publish()
    {
        // 获取当前登录用户
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }

        // 判断是否为 B 端用户：在 esop_b_accounts 表存在记录即为 B 端
        $bAccountId = EsopBAccount::whereRaw("FIND_IN_SET({$user->id}, user_ids)")
            ->value('id');

        if (!$bAccountId) {
            $this->error('仅 B 端用户可发布公告');
        }

        // 接收并校验参数
        $title   = $this->request->post('title/s', '');
        $content = $this->request->post('content/s', '');
        $image   = $this->request->post('image/s', '');

        if ($title === '' || $content === '') {
            $this->error('标题和内容均不能为空');
        }

        // 封装数据
        $article               = new EsopArticle();
        $article->title        = $title;
        $article->content      = $content;
        $article->summary      = mb_substr(strip_tags($content), 0, 100);
        $article->cover_image  = $image;
        $article->board_id     = $this->board_id;  // 公告中心
        $article->b_account_id = $bAccountId;      // 归属 B 端
        $article->status       = 0;                // 0 = 待审批
        $article->created_at   = date('Y-m-d H:i:s');
        $article->category_id  = 0;
        $article->author_id    = $user->id;

        // 保存公告
        if (!$article->save()) {
            $this->error('发布失败，请稍后重试');
        }

        // 创建待审批记录
        EsopNoticeApprovalRecord::createPending($article->id, $user->id);

        $this->success('提交成功，等待审批', ['id' => $article->id]);
    }
} 