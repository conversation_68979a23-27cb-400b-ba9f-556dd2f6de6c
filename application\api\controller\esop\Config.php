<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopSystemConfig;
use app\common\model\UiStyle;

/**
 * 系统配置接口
 */
class Config extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    
    /**
     * 获取系统配置
     */
    public function info()
    {
        $config = EsopSystemConfig::getConfig();
        
        // 过滤敏感信息
        if (isset($config['stock_price_api_url']) && $config['stock_price_api_url']) {
            $config['has_stock_api'] = true;
            unset($config['stock_price_api_url']);
        } else {
            $config['has_stock_api'] = false;
        }
        
        if (isset($config['exchange_rate_api_url']) && $config['exchange_rate_api_url']) {
            $config['has_exchange_api'] = true;
            unset($config['exchange_rate_api_url']);
        } else {
            $config['has_exchange_api'] = false;
        }
        
        // 处理LOGO完整URL
        if (isset($config['logo_url']) && $config['logo_url']) {
            $config['logo_url'] = cdnurl($config['logo_url'], true);
        }
        
        return $this->jsonSucess('获取成功', $config);
    }
    
    /**
     * 获取APP下载地址
     */
    public function appDownload()
    {
        $config = EsopSystemConfig::getConfig();
        
        $data = [
            'android' => $config['android_app_url'] ?? '',
            'ios' => $config['ios_app_url'] ?? '',
        ];
        
        return $this->jsonSucess('获取成功', $data);
    }

    /**
     * 获取API文档
     */
    public function apiDoc()
    {
        $file = file_get_contents(ROOT_PATH . '.http');
        // 检查文件是否存在
        if (!file_exists(ROOT_PATH . '.http')) {
            return $this->toError('API文档文件不存在');
        }
        
        // 获取文件内容
        $content = file_get_contents(ROOT_PATH . '.http');
        
        // 检查文件内容是否为空
        if (empty($content)) {
            return $this->toError('API文档文件内容为空');
        }
        
        // 设置响应头，告诉浏览器这是一个需要下载的文件
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="api_documentation.http"');
        header('Content-Length: ' . strlen($content));
        
        // 输出文件内容
        echo $content;
        exit;
    }
    
    /**
     * 获取关于我们信息
     */
    public function about()
    {
        $config = EsopSystemConfig::getConfig();
        
        $aboutUs = $config['about_us'] ?? '';
        
        return $this->jsonSucess('获取成功', ['about_us' => $aboutUs]);
    }

    /**
     * 获取注销用户风险提示
     */
    public function deleteAccountRisk()
    {
        $config = EsopSystemConfig::getConfig();
        $data = [
            'risk_tips' => $config['delete_risk_tips'] ?? '',
        ];
        return $this->jsonSucess('获取成功', $data);
    }

    /**
     * 获取隐私政策
     */
    public function privacyPolicy()
    {
        $config = EsopSystemConfig::getConfig();
        $data = [
            'privacy_policy' => $config['privacy_policy'] ?? '',
        ];
        return $this->jsonSucess('获取成功', $data);
    }

    /**
     * 获取用户协议
     */
    public function userAgreement()
    {
        $config = EsopSystemConfig::getConfig();
        $data = [
            'user_agreement' => $config['user_agreement'] ?? '',
        ];
        return $this->jsonSucess('获取成功', $data);
    }
    
    /**
     * 获取平台UI风格
     * 如果不存在默认风格(is_default=1)，则返回第一条记录
     */
    public function defaultStyle()
    {
        // 获取默认风格
        $uiStyle = UiStyle::getDefault();
        
        // 如果默认风格不存在，则获取第一条记录
        if (empty($uiStyle)) {
            $uiStyle = UiStyle::order('id', 'asc')->find();
        }
        
        // 如果仍然没有找到记录，返回空对象
        if (empty($uiStyle)) {
            return $this->jsonSucess('获取成功', (object)[]); 
        }
        
        return $this->jsonSucess('获取成功', $uiStyle);
    }
    
    /**
     * 获取UI风格列表
     * 
     * @ApiTitle    (获取UI风格列表)
     * @ApiSummary  (获取系统中所有可用的UI风格列表，支持分页)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认为1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页记录数，默认为10")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"1688888888","data":{"total":4,"per_page":10,"current_page":1,"last_page":1,"data":[{"id":1,"style_name":"中国红","main_color":"#FF0000","is_default":1,...}]}})
     */
    public function uiStyleList()
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);
        
        // 查询所有未删除的UI风格
        $query = UiStyle::where([]);
        
        // 获取总记录数
        $total = $query->count();
        
        // 分页查询数据
        $list = $query->order('is_default', 'desc')
            ->order('id', 'asc')
            ->page($page, $limit)
            ->select();
        
        // 处理图片URL
        foreach ($list as &$item) {
            if (!empty($item['home_banner_url'])) {
                $item['home_banner_url'] = cdnurl($item['home_banner_url'], true);
            }
            if (!empty($item['inner_banner_url'])) {
                $item['inner_banner_url'] = cdnurl($item['inner_banner_url'], true);
            }
            if (!empty($item['member_banner_url'])) {
                $item['member_banner_url'] = cdnurl($item['member_banner_url'], true);
            }
        }
        
        // 构建分页数据
        $result = [
            'total' => $total,
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => ceil($total / $limit),
            'data' => $list
        ];
        
        return $this->jsonSucess('获取成功', $result);
    }
} 