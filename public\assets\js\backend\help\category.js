define(['jquery','bootstrap','backend','table','form'],function($,undefined,Backend,Table,Form){
    var Controller={
        index:function(){
            Table.api.init({extend:{
                index_url:'help/category/index'+location.search,
                add_url:'help/category/add',
                edit_url:'help/category/edit',
                del_url:'help/category/del',
                multi_url:'help/category/multi',
                table:'esop_article_categories'
            }});
            var table=$("#table");
            table.bootstrapTable({
                url:$.fn.bootstrapTable.defaults.extend.index_url,
                pk:'id',
                sortName:'id',
                columns:[[
                    {checkbox:true},
                    {field:'id',title:'ID',sortable:true},
                    {field:'category_name',title:'分类名称',operate:'LIKE'},
                    {field:'created_at',title:'创建时间',operate:'RANGE',addclass:'datetimerange',formatter:Table.api.formatter.datetime},
                    {field:'operate',title:'操作',table:table,events:Table.api.events.operate,formatter:Table.api.formatter.operate}
                ]]
            });
            Table.api.bindevent(table);
        },
        add:function(){Controller.api.bindevent();},
        edit:function(){Controller.api.bindevent();},
        api:{
            bindevent:function(){Form.api.bindevent($('form[role=form]'));}
        }
    };return Controller;
}); 