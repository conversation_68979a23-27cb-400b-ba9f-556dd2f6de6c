<?php

namespace app\common\model;

use think\Model;

/**
 * 系统基础配置模型
 */
class EsopSystemConfig extends Model
{
    // 表名
    protected $name = 'esop_system_configs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 获取系统配置
     * 
     * @return array 系统配置数据
     */
    public static function getConfig()
    {
        $config = self::order('id', 'desc')->find();
        $data = $config ? $config->toArray() : [];
        // 设置默认值，防止未定义
        $defaults = [
            'transfer_control' => '0', // 默认全部
            'transfer_approval_switch' => '0', // 默认关闭
            // 其他默认值...
        ];
        return array_merge($defaults, $data);
    }
    
    /**
     * 更新系统配置
     * 
     * @param array $data 系统配置数据
     * @return boolean
     */
    public static function updateConfig($data)
    {
        $config = self::order('id', 'desc')->find();
        
        if ($config) {
            $config->save($data);
            return true;
        } else {
            return self::create($data) ? true : false;
        }
    }
    
    /**
     * 上传LOGO
     * 
     * @param object $file 上传文件对象
     * @return string|false 成功返回文件路径，失败返回false
     */
    public static function uploadLogo($file)
    {
        if (empty($file)) {
            return false;
        }
        
        $uploadDir = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'system';
        
        // 检查上传目录
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // 生成文件名
        $fileName = 'logo_' . date('YmdHis') . rand(1000, 9999);
        
        // 获取文件后缀
        $suffix = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
        if (!in_array($suffix, ['jpg', 'jpeg', 'png', 'gif'])) {
            return false;
        }
        
        $fileName .= '.' . $suffix;
        
        // 保存文件
        $info = $file->move($uploadDir, $fileName);
        
        if ($info) {
            // 返回文件访问路径
            return '/uploads/system/' . $fileName;
        } else {
            return false;
        }
    }
} 