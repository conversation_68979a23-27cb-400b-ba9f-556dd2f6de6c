<?php

namespace app\admin\controller\esopuser;

use app\admin\model\Admin;
use app\common\controller\Backend;
use app\common\model\EsopUser as EsopUserModel;
use app\common\model\EsopUserProfile as EsopUserProfileModel;
use app\common\model\EsopIdCardImages as EsopIdCardImagesModel;
use app\common\model\EsopUserAsset;
use app\common\model\EsopUserStockRecords;
use app\common\model\EsopStockManagement;
use app\common\model\EsopInvitationRelation;
use think\Db;
use think\Exception;

/**
 * ESOP用户管理控制器
 *
 * @icon fa fa-users
 */
class User extends Backend
{
    /**
     * EsopUser模型对象
     * @var \app\common\model\EsopUser
     */
    protected $model = null;
    
    /**
     * 默认排序方式
     */
    protected $sort = 'id';
    protected $order = 'DESC';

    /**
     * 初始化方法
     */
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\EsopUser;
        
        // 分配性别选项到模板
        $this->view->assign("genderList", [
            0 => __('保密'),
            1 => __('男'),
            2 => __('女')
        ]);
        
        // 分配婚姻状况选项到模板
        $this->view->assign("maritalStatusList", [
            0 => __('保密'),
            1 => __('未婚'),
            2 => __('已婚'),
            3 => __('离异'),
            4 => __('丧偶')
        ]);
        
        // 分配审核状态选项到模板
        $this->view->assign("auditStatusList", [
            0 => __('待审核'),
            1 => __('审核通过'),
            2 => __('审核不通过')
        ]);
        
        // 分配状态选项到模板
        $this->view->assign("statusList", [
            '1' => __('正常'),
            '0' => __('禁用')
        ]);
    }
    
    /**
     * 查看用户列表
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            // 获取搜索参数
            $filter = json_decode($this->request->get('filter'), true);
            $op = json_decode($this->request->get('op'), true);
            
            // 构建用户查询
            // 构建用户查询，使用FastAdmin推荐的Db方式，保证兼容性和可维护性
            // 这里直接使用模型的静态方法进行查询，避免直接调用query()带来的兼容性问题
            $userQuery = $this->model;
            
            // 添加手机号搜索条件
            if (!empty($filter['phone'])) {
                $op['phone'] = isset($op['phone']) ? $op['phone'] : 'LIKE';
                $userQuery->where('phone', strtoupper($op['phone']), "%{$filter['phone']}%");
            }
            
            // 添加状态搜索条件
            if (isset($filter['status']) && $filter['status'] !== '') {
                $userQuery->where('status', $filter['status']);
            }
            
            // 获取用户IDs列表
            $profileFilter = [];
            if (!empty($filter['real_name'])) {
                $profileFilter['real_name'] = $filter['real_name'];
                $op_real_name = isset($op['real_name']) ? $op['real_name'] : 'LIKE';
                $profileFilter['op_real_name'] = $op_real_name;
            }
            // 移除身份证号搜索功能
            // if (!empty($filter['id_card_no'])) {
            //     $profileFilter['id_card_no'] = $filter['id_card_no'];
            //     $op_id_card_no = isset($op['id_card_no']) ? $op['id_card_no'] : 'LIKE';
            //     $profileFilter['op_id_card_no'] = $op_id_card_no;
            // }
            if (isset($filter['audit_status']) && $filter['audit_status'] !== '') {
                $profileFilter['audit_status'] = $filter['audit_status'];
            }
            
            // 如果有资料相关的筛选条件，先查询符合条件的用户ID
            $filteredUserIds = [];
            if (!empty($profileFilter)) {
                $profileQuery = EsopUserProfileModel::where([]);
                if (!empty($profileFilter['real_name'])) {
                    $profileQuery->where('real_name', $profileFilter['op_real_name'], "%{$profileFilter['real_name']}%");
                }
                // 移除身份证号查询条件
                // if (!empty($profileFilter['id_card_no'])) {
                //     $profileQuery->where('id_card_no', $profileFilter['op_id_card_no'], "%{$profileFilter['id_card_no']}%");
                // }
                if (isset($profileFilter['audit_status'])) {
                    $profileQuery->where('audit_status', $profileFilter['audit_status']);
                }
                
                $filteredUserIds = $profileQuery->column('user_id');
                if (empty($filteredUserIds)) {
                    // 没有找到符合条件的用户资料，返回空结果
                    return json(['total' => 0, 'rows' => []]);
                }
                
                $userQuery->where('id', 'in', $filteredUserIds);
            }
            
            // 分页处理
            $sort = $this->request->get("sort", "");
            $order = $this->request->get("order", "");
            $offset = $this->request->get("offset", 0);
            $limit = $this->request->get("limit", 10);
            
            // 排序
            if ($sort && $order) {
                $userQuery->order($sort, $order);
            } else {
                $userQuery->order($this->sort, $this->order);
            }

            // 获取分页数据
            $users = $userQuery->limit($offset, $limit)->paginate();

           
            
            // 获取用户ID数组
            $userIds = [];
            foreach ($users as $user) {
                $userIds[] = $user->id;
            }

            // 获取用户资料
            $profiles = [];
            if (!empty($userIds)) {
                $profileList = EsopUserProfileModel::where('user_id', 'in', $userIds)->select();
                foreach ($profileList as $profile) {
                    // 统一将查询结果转为数组
                    if ($profile instanceof \think\Model) {
                        $profiles[$profile->user_id] = $profile->toArray();
                    } else {
                        $profiles[$profile['user_id']] = $profile;
                    }
                }
            }

            // 批量获取用户资产信息
            $userAssets = [];
            if (!empty($userIds)) {
                $assetList = EsopUserAsset::where('user_id', 'in', $userIds)->select();
                foreach ($assetList as $asset) {
                    $userAssets[$asset->user_id] = $asset->toArray();
                }
            }

            // 批量获取持有股票详情（显示多个不同股票的数量）
            $stockDetails = [];
            if (!empty($userIds)) {
                $stockList = EsopUserStockRecords::alias('usr')
                    ->join('esop_stock_management sm', 'usr.stock_id = sm.id', 'LEFT')
                    ->where('usr.user_id', 'in', $userIds)
                    ->where('usr.amount', '>', 0)
                    ->field([
                        'usr.user_id',
                        'GROUP_CONCAT(CONCAT(sm.stock_name, "(", usr.amount, ")") SEPARATOR ", ") as stock_details'
                    ])
                    ->group('usr.user_id')
                    ->select();
                foreach ($stockList as $stock) {
                    $stockDetails[$stock['user_id']] = $stock['stock_details'];
                }
            }

            // 批量计算股票市值
            $stockValues = [];
            if (!empty($userIds)) {
                $stockRecords = EsopUserStockRecords::alias('r')
                    ->join('esop_stock_management s', 'r.stock_id = s.id', 'LEFT')
                    ->where('r.user_id', 'in', $userIds)
                    ->where('r.amount', '>', 0)
                    ->field('r.user_id, SUM(r.amount * s.stock_price) as total_value')
                    ->group('r.user_id')
                    ->select();
                foreach ($stockRecords as $record) {
                    $stockValues[$record['user_id']] = $record['total_value'] ?: 0;
                }
            }

            // 批量获取推荐人信息
            $inviterInfo = [];
            if (!empty($userIds)) {
                $invitationList = EsopInvitationRelation::alias('ir')
                    ->join('esop_users u', 'ir.inviter_id = u.id', 'LEFT')
                    ->join('esop_user_profiles up', 'u.id = up.user_id', 'LEFT')
                    ->where('ir.invitee_id', 'in', $userIds)
                    ->field('ir.invitee_id, u.phone as inviter_phone, up.real_name as inviter_name')
                    ->select();
                foreach ($invitationList as $invitation) {
                    $inviterName = $invitation['inviter_name'] ?: '';
                    $inviterPhone = $invitation['inviter_phone'] ?: '';
                    $inviterInfo[$invitation['invitee_id']] = $inviterName ?
                        "{$inviterName}({$inviterPhone})" : ($inviterPhone ?: '无');
                }
            }
            
                            // 合并用户数据和用户资料
            $rows = [];
            foreach ($users as $user) {
                // 基本用户数据
                $userData = $user->toArray();

                // 添加用户资料数据
                if (isset($profiles[$user->id])) {
                    $profile = $profiles[$user->id];
                    $userData['real_name'] = $profile['real_name'] ?? '';
                    $userData['nickname'] = $profile['nickname'] ?? '';
                    $userData['gender'] = $profile['gender'] ?? 0;
                    $userData['birth_date'] = $profile['birth_date'] ?? '';
                    // 移除身份证字段显示
                    // $userData['id_card_no'] = $profile['id_card_no'] ?? '';
                    $userData['audit_status'] = $profile['audit_status'] ?? 0;

                    // 添加性别文本
                    $genderMap = [0 => __('保密'), 1 => __('男'), 2 => __('女')];
                    $userData['gender_text'] = isset($genderMap[$profile['gender']]) ? $genderMap[$profile['gender']] : __('保密');

                    // 添加审核状态文本
                    $statusMap = [0 => __('待审核'), 1 => __('审核通过'), 2 => __('审核不通过')];
                    $userData['audit_status_text'] = isset($statusMap[$profile['audit_status']]) ? $statusMap[$profile['audit_status']] : __('待审核');
                } else {
                    // 默认值
                    $userData['real_name'] = '';
                    $userData['nickname'] = substr($user->phone, 0, 3) . '****' . substr($user->phone, -4);
                    $userData['gender'] = 0;
                    $userData['gender_text'] = __('保密');
                    $userData['birth_date'] = '';
                    // 移除身份证字段显示
                    // $userData['id_card_no'] = '';
                    $userData['audit_status'] = 0;
                    $userData['audit_status_text'] = __('待审核');
                }

                // 添加状态文本，兼容字符串和数字
                $statusList = [
                    '1' => __('正常'),
                    '0' => __('禁用')
                ];
                $userData['status_text'] = isset($statusList[$userData['status']]) ? $statusList[$userData['status']] : __('未知');

                // 添加新的字段
                $assetData = isset($userAssets[$user->id]) ? $userAssets[$user->id] : [];
                $availableAssets = $assetData['available_assets'] ?? 0;
                $pendingAssets = $assetData['pending_assets'] ?? 0;
                $lockedAssets = $assetData['locked_assets'] ?? 0;
                $stockValue = isset($stockValues[$user->id]) ? $stockValues[$user->id] : 0;

                // 1. 待解禁金额（直接取EsopUserAsset的pending_assets）
                $userData['pending_assets'] = number_format($pendingAssets, 2);

                // 2. 可换股数（取EsopUserAsset的available_assets）
                $userData['available_assets'] = number_format($availableAssets, 2);

                // 3. 股票（持有股票数量 - 显示多个不同股票的数量）
                $userData['stock_details'] = isset($stockDetails[$user->id]) ? $stockDetails[$user->id] : '无';

                // 4. 总资产
                // 使用 bcadd 计算总资产保持精度
                $totalAssets = bcadd(
                    bcadd(
                        bcadd((string)$availableAssets, (string)$pendingAssets, 2),
                        (string)$lockedAssets, 2
                    ),
                    (string)$stockValue, 2
                );
                $userData['total_assets'] = number_format($totalAssets, 2);

                // 5. 推荐人信息
                $userData['inviter_info'] = isset($inviterInfo[$user->id]) ? $inviterInfo[$user->id] : '无';

                $rows[] = $userData;
            }
            
            $result = ["total" => $users->total(), "rows" => $rows];
            
            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 添加用户
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 检查手机号是否存在
                if ($this->model->where('phone', $params['phone'])->find()) {
                    $this->error(__('手机号已存在'));
                }
                
                Db::startTrans();
                try {
                    // 创建用户
                    $userParams = [
                        'phone' => $params['phone'],
                        'password' => $params['password'],
                        'status' => $params['status'] ?? '1',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    // 添加用户
                    $user = $this->model->create($userParams);
                    
                    // 创建用户资料 - 确保填写基本信息
                    $profileParams = [
                        'real_name' => $params['real_name'] ?? '',
                        'gender' => $params['gender'] ?? 0,
                        'birth_date' => $params['birth_date'] ?? null,
                        'id_card_no' => $params['id_card_no'] ?? '',
                        'marital_status' => $params['marital_status'] ?? 0,
                        'securities_account' => $params['securities_account'] ?? '',
                        'nickname' => $params['nickname'] ?? '',
                        'avatar' => $params['avatar'] ?? '/assets/img/avatar.png',
                        'audit_status' => $params['audit_status'] ?? 0,
                        'bank_name' => $params['bank_name'] ?? '',
                        'bank_branch' => $params['bank_branch'] ?? '',
                        'bank_account' => $params['bank_account'] ?? '',
                        'bank_image' => $params['bank_image'] ?? '',
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    // 如果没有提供昵称，使用默认值
                    if (empty($profileParams['nickname'])) {
                        if (!empty($profileParams['real_name'])) {
                            $profileParams['nickname'] = $profileParams['real_name'];
                        } else {
                            // 使用手机号脱敏作为昵称
                            $profileParams['nickname'] = substr($params['phone'], 0, 3) . '****' . substr($params['phone'], -4);
                        }
                    }
                    
                    // 如果提供了身份证图片，也一同保存
                    if (isset($params['front_image']) || isset($params['back_image'])) {
                        $profileParams['front_image'] = $params['front_image'] ?? '';
                        $profileParams['back_image'] = $params['back_image'] ?? '';
                    }
                    
                    // 创建用户资料记录
                    $userProfile = EsopUserProfileModel::createOrUpdate($user->id, $profileParams);
                    
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('参数错误'));
        }
        return $this->view->fetch();
    }
    
    /**
     * 编辑用户
     */
    public function edit($ids = null)
    {
        $row = $this->model->getActiveUser($ids);
        if (!$row) {
            $this->error(__('记录未找到或已删除'));
        }
        
        // 获取用户资料
        $profile = EsopUserProfileModel::getByUserId($ids);
        if (!$profile) {
            // 如果用户资料不存在，提供默认值
            $profile = [
                'real_name' => '',
                'nickname' => substr($row->phone, 0, 3) . '****' . substr($row->phone, -4),
                'gender' => 0,
                'birth_date' => null,
                'id_card_no' => '',
                'marital_status' => 0,
                'securities_account' => '',
                'audit_status' => 0,
                'audit_remark' => '',
                'bank_name' => '',
                'bank_branch' => '',
                'bank_account' => '',
                'bank_image' => '',
                'avatar' => '/assets/img/avatar.png'
            ];
        } else {
            $profile = $profile->toArray();
        }
        
        // 获取身份证图片
        $idCardImages = EsopUserProfileModel::getIdCardImages($ids);

        // 获取推荐人信息
        $inviterInfo = EsopInvitationRelation::alias('ir')
            ->join('esop_users u', 'ir.inviter_id = u.id', 'LEFT')
            ->join('esop_user_profiles up', 'u.id = up.user_id', 'LEFT')
            ->where('ir.invitee_id', $ids)
            ->field('ir.inviter_id, u.phone as inviter_phone, up.real_name as inviter_name')
            ->find();

        $inviterData = [];
        if ($inviterInfo) {
            $inviterData['inviter_id'] = $inviterInfo['inviter_id'];
            $inviterData['inviter_display'] = $inviterInfo['inviter_name'] ?
                "{$inviterInfo['inviter_name']}({$inviterInfo['inviter_phone']})" :
                ($inviterInfo['inviter_phone'] ?: '');
        } else {
            $inviterData['inviter_id'] = '';
            $inviterData['inviter_display'] = '';
        }

        // 合并用户数据、资料和身份证图片（用于视图展示）
        $rowData = array_merge($row->toArray(), $profile, $idCardImages, $inviterData);
        
        // ------------ 关键修复 -------------
        // 数据库中 status 应该是 0/1，不需要转换为 normal/hidden
        // 删除错误的转换代码
        // ----------------------------------
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 检查手机号是否被其他用户使用
                if (isset($params['phone']) && $this->model->where('phone', $params['phone'])->where('id', '<>', $ids)->find()) {
                    $this->error(__('手机号已被其他用户使用'));
                }
                
                Db::startTrans();
                try {
                    // 更新用户信息
                    $userParams = [];
                    
                    if (isset($params['phone'])) $userParams['phone'] = $params['phone'];
                    if (isset($params['status'])) $userParams['status'] = $params['status'];
                    if (!empty($params['password'])) $userParams['password'] = $params['password'];
                    
                    if (!empty($userParams)) {
                        // 更新用户信息
                        $userModel = $this->model->get($ids); // 重新获取用户模型对象
                        foreach ($userParams as $key => $value) {
                            $userModel->$key = $value;
                        }
                        $userModel->save();
                    }
                    
                    // 更新用户资料和身份证图片
                    $profileParams = [];
                    
                    if (isset($params['real_name'])) $profileParams['real_name'] = $params['real_name'];
                    if (isset($params['gender'])) $profileParams['gender'] = $params['gender'];
                    if (isset($params['birth_date'])) $profileParams['birth_date'] = $params['birth_date'];
                    if (isset($params['id_card_no'])) $profileParams['id_card_no'] = $params['id_card_no'];
                    if (isset($params['marital_status'])) $profileParams['marital_status'] = $params['marital_status'];
                    if (isset($params['securities_account'])) $profileParams['securities_account'] = $params['securities_account'];
                    if (isset($params['nickname'])) $profileParams['nickname'] = $params['nickname'];
                    if (isset($params['audit_status'])) $profileParams['audit_status'] = $params['audit_status'];
                    if (isset($params['front_image'])) $profileParams['front_image'] = $params['front_image'];
                    if (isset($params['back_image'])) $profileParams['back_image'] = $params['back_image'];
                    
                    // 添加银行卡相关字段
                    if (isset($params['bank_name'])) $profileParams['bank_name'] = $params['bank_name'];
                    if (isset($params['bank_branch'])) $profileParams['bank_branch'] = $params['bank_branch'];
                    if (isset($params['bank_account'])) $profileParams['bank_account'] = $params['bank_account'];
                    if (isset($params['bank_image'])) $profileParams['bank_image'] = $params['bank_image'];
                    
                    if (!empty($profileParams)) {
                        EsopUserProfileModel::createOrUpdate($ids, $profileParams);
                    }

                    // 处理推荐人更新
                    if (isset($params['inviter_id'])) {
                        $newInviterId = $params['inviter_id'];

                        // 获取当前的邀请关系
                        $currentRelation = EsopInvitationRelation::where('invitee_id', $ids)->find();
                        $currentInviterId = $currentRelation ? $currentRelation->inviter_id : null;

                        // 处理空值：将空字符串转换为null以便比较
                        $newInviterId = empty($newInviterId) ? null : $newInviterId;

                        // 只有当新的邀请人ID与当前邀请人ID不同时才需要更新
                        if ($newInviterId != $currentInviterId) {
                            if ($newInviterId) {
                                // 验证新推荐人是否存在且不是自己
                                if ($newInviterId != $ids) {
                                    //判断邀请人是不是b端账号管理员
                                    $inviterIsBAdmin = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$newInviterId}, user_ids)")
                                    ->value('id') ? true : false;

                                    // 获取新的邀请人的邀请关系
                                    $newInviteeRelation = EsopInvitationRelation::where('invitee_id', $newInviterId)->find();

                                    // 获取新邀请人的邀请码，如果没有则新建一个（从invitationcode表读取）
                                    $invitationCode = '';
                                    // 读取邀请码表
                                    $invitationCodeRow = \app\common\model\EsopInvitationCode::where('user_id', $newInviterId)->limit(1)->find();
                                    if ($invitationCodeRow && !empty($invitationCodeRow->code)) {
                                        $invitationCode = $invitationCodeRow->code;
                                    } else {
                                        // 生成新的邀请码
                                        $codes = \app\common\model\EsopInvitationCode::generateCodes(1, '团队邀请', $newInviterId);
                                        $invitationCode = $codes[0];
                                    }

                                    if (!$currentRelation) {
                                        $currentRelation = new EsopInvitationRelation();
                                        $currentRelation->invitee_id = $ids;
                                        $currentRelation->created_at = date('Y-m-d H:i:s');
                                    }

                                    // 更新现有关系
                                    $currentRelation->inviter_id = $newInviterId;
                                    $currentRelation->updated_at = date('Y-m-d H:i:s');
                                    $currentRelation->invitation_code = $invitationCode;
                                    $currentRelation->root_id = $newInviteeRelation ? ( !$inviterIsBAdmin ? $newInviteeRelation->root_id : trim("{$newInviteeRelation->root_id},{$newInviterId}", ',') ) : ($inviterIsBAdmin ? $newInviterId : null);
                                    if(empty($currentRelation->root_id)){
                                       throw new \Exception('推荐人更新失败');
                                    }
                                    $currentRelation->save();
                                }
                            }
                        }
                    }

                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                $this->success();
            }
            $this->error(__('参数错误'));
        }
        
        $this->view->assign("row", $rowData);
        return $this->view->fetch();
    }
    
    /**
     * 删除用户
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("请求错误"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        if (!$ids) {
            $this->error(__('未选择记录'));
        }
        
        Db::startTrans();
        try {
            $idArr = explode(',', $ids);
            foreach ($idArr as $id) {
                // 软删除用户
                $this->model->where('id', $id)->delete();
                
                // 软删除用户资料和身份证图片
                EsopUserProfileModel::softDeleteByUserId($id);  
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success();
    }
    
    /**
     * 查看用户详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->getActiveUser($ids);
        if (!$row) {
            $this->error(__('记录未找到或已删除'));
        }
        
        // 获取用户资料
        $profile = EsopUserProfileModel::getByUserId($ids);
        if (!$profile) {
            // 如果用户资料不存在，提供默认值
            $profile = [
                'real_name' => '',
                'nickname' => substr($row->phone, 0, 3) . '****' . substr($row->phone, -4),
                'gender' => 0,
                'gender_text' => __('保密'),
                'birth_date' => date('Y-m-d'), // 设置为当前日期，避免数据库错误
                'id_card_no' => '',
                'marital_status' => 0,
                'marital_status_text' => __('保密'),
                'securities_account' => '',
                'audit_status' => 0,
                'audit_status_text' => __('待审核'),
                'audit_remark' => '',
                'bank_name' => '',
                'bank_branch' => '',
                'bank_account' => '',
                'bank_image' => '',
                'avatar' => '/assets/img/avatar.png',
                'audit_time' => null // 添加审核时间字段默认值
            ];
        } else {
            $profile = $profile->toArray();
            
            // 添加状态文本转换
            // 审核状态文本
            $auditStatusMap = [0 => __('待审核'), 1 => __('审核通过'), 2 => __('审核不通过')];
            $profile['audit_status_text'] = isset($auditStatusMap[$profile['audit_status']]) ? $auditStatusMap[$profile['audit_status']] : __('待审核');
            
            // 性别文本
            $genderMap = [0 => __('保密'), 1 => __('男'), 2 => __('女')];
            $profile['gender_text'] = isset($genderMap[$profile['gender']]) ? $genderMap[$profile['gender']] : __('保密');
            
            // 婚姻状态文本
            $maritalMap = [0 => __('保密'), 1 => __('未婚'), 2 => __('已婚'), 3 => __('离异'), 4 => __('丧偶')];
            $profile['marital_status_text'] = isset($maritalMap[$profile['marital_status']]) ? $maritalMap[$profile['marital_status']] : __('保密');
        }
        
        // 获取身份证图片
        $idCardImages = EsopUserProfileModel::getIdCardImages($ids);

        // 获取推荐人信息
        $inviterInfo = EsopInvitationRelation::alias('ir')
            ->join('esop_users u', 'ir.inviter_id = u.id', 'LEFT')
            ->join('esop_user_profiles up', 'u.id = up.user_id', 'LEFT')
            ->where('ir.invitee_id', $ids)
            ->field('ir.inviter_id, u.phone as inviter_phone, up.real_name as inviter_name')
            ->find();

        $inviterData = [];
        if ($inviterInfo) {
            $inviterData['inviter_display'] = $inviterInfo['inviter_name'] ?
                "{$inviterInfo['inviter_name']}({$inviterInfo['inviter_phone']})" :
                ($inviterInfo['inviter_phone'] ?: '无');
        } else {
            $inviterData['inviter_display'] = '无';
        }

        // 合并用户数据、资料和身份证图片
        $row = array_merge($row->toArray(), $profile, $idCardImages, $inviterData);
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
    
    /**
     * 审核用户实名认证
     */
    public function audit($ids = null)
    {
        $row = $this->model->getActiveUser($ids);
        if (!$row) {
            $this->error(__('记录未找到或已删除'));
        }
        
        // 获取用户资料
        $profile = EsopUserProfileModel::getByUserId($ids);
        if (!$profile) {
            // 如果用户资料不存在，先创建一个空记录
            $defaultProfile = [
                'user_id' => $ids,
                'real_name' => '',
                'nickname' => substr($row->phone, 0, 3) . '****' . substr($row->phone, -4),
                'gender' => 0,
                'birth_date' => date('Y-m-d'), // 设置为当前日期，避免数据库错误
                'id_card_no' => '',
                'marital_status' => 0,
                'audit_status' => 0,
                'audit_time' => null, // 添加审核时间字段默认值
                'created_at' => date('Y-m-d H:i:s')
            ];
            $profile = EsopUserProfileModel::createOrUpdate($ids, $defaultProfile);
            if (!$profile) {
                $this->error(__('创建用户资料失败'));
            }
        }
        
        // 获取身份证图片
        $idCardImages = EsopUserProfileModel::getIdCardImages($ids);
        
        // 合并用户数据、资料和身份证图片
        $row = array_merge($row->toArray(), $profile->toArray(), $idCardImages);
        
        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            if (!isset($params['audit_status'])) {
                $this->error(__('审核状态不能为空'));
            }
            
            $audit_status = intval($params['audit_status']);
            $audit_remark = $params['audit_remark'] ?? '';
            
            if (!in_array($audit_status, [1, 2])) {
                $this->error(__('无效的审核状态'));
            }
            
            Db::startTrans();
            try {
                // 更新资料审核状态
                $profile->audit_status = $audit_status;
                $profile->audit_remark = $audit_remark;
                $profile->audit_time = date('Y-m-d H:i:s');
                $profile->audit_user_id = $this->auth->id;
                $profile->save();
                
                // 更新身份证图片验证状态
                if ($audit_status == 1) { // 如果审核通过
                    $idCardModel = new EsopIdCardImagesModel();
                    $idCardImage = $idCardModel->where('user_id', $ids)->find();
                    if ($idCardImage) {
                        $idCardImage->is_verified = 1; // 设置为已验证
                        $idCardImage->save();
                    }
                }
                
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            
            $this->success();
        }
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
    
    /**
     * 重置密码
     */
    public function resetpwd($ids = null)
    {
        $row = $this->model->getActiveUser($ids);
        if (!$row) {
            $this->error(__('记录未找到或已删除'));
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if ($params['password'] != $params['password_confirm']) {
                    $this->error(__('两次输入的密码不一致'));
                }
                
                $row->password = $params['password'];
                $row->save();
                $this->success();
            }
            $this->error(__('参数错误'));
        }
        
        $this->view->assign('row', $row);
        return $this->view->fetch();
    }

   /**
     * 获取用户列表（用于后台选择器）
     */
    public function select()
    {
        $this->request->filter(['strip_tags', 'trim']);
        
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则返回符合选择器格式的数据
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
        }
        
        return $this->error('请求错误');
    }
    
    /**
     * 用户资金详情
     * 显示用户的资金信息和股票持有情况
     */
    public function finance_detail($ids = null)
    {
        $row = $this->model->getActiveUser($ids);
        if (!$row) {
            $this->error(__('记录未找到或已删除'));
        }

        /*
         * Ajax请求：返回股票列表分页数据
         * Bootstrap-table 会自动携带 offset、limit、sort、order 等参数
         */
        if ($this->request->isAjax()) {
            // 基础分页参数
            $sort   = $this->request->get("sort", "id");
            $order  = $this->request->get("order", "desc");
            $offset = $this->request->get("offset", 0);
            $limit  = $this->request->get("limit", 10);

            // 构建查询（基础条件）
            $stockList = EsopUserStockRecords::with(['stock' => function($query) {
                    $query->field('id,stock_name,stock_code,price_type,is_on_shelf');
                }])
                ->where('user_id', $ids)
                ->where('amount', '>', 0)
                ->order($sort, $order)
                ->paginate(['list_rows' => $limit, 'page' => $offset]);

            // 格式化返回数据
            $rows = [];
            foreach ($stockList->items() as $stock) {
                $stockData               = $stock->toArray();
                $stockData['stock_name'] = $stockData['stock']['stock_name'] ?? '';
                $stockData['stock_code'] = $stockData['stock']['stock_code'] ?? '';
                unset($stockData['stock']);
                $rows[] = $stockData;
            }

            return json(['total' => $stockList->total(), 'rows' => $rows]);
        }

        // ---------------- 普通页面渲染 ----------------
        // 获取用户资料
        $profile = EsopUserProfileModel::getByUserId($ids);
        if (!$profile) {
            $profile = [
                'real_name' => '',
                'nickname'  => substr($row->phone, 0, 3) . '****' . substr($row->phone, -4),
            ];
        } else {
            $profile = $profile->toArray();
        }

        // 资产信息
        $financeData = EsopUserAsset::where('user_id', $ids)
            ->order('id', 'desc')
            ->find();

        // ================= 计算股票市值 & 总资产 =================
        // 查询股票总价值：SUM(amount * stock_price)
        $stockValue = EsopUserStockRecords::alias('r')
            ->join('esop_stock_management s', 'r.stock_id = s.id', 'LEFT')
            ->where('r.user_id', $ids)
            ->value(Db::raw('SUM(r.amount * s.stock_price)'));
        $stockValue = $stockValue ?: 0;

        // 资金三项
        $available = $financeData['available_assets'] ?? 0;
        $pending   = $financeData['pending_assets'] ?? 0;
        $locked    = $financeData['locked_assets'] ?? 0;

        // 使用 bc 计算保持精度
        $totalAssets = bcadd(bcadd(bcadd((string)$available, (string)$pending, 2), (string)$locked, 2), (string)$stockValue, 2);
        // ========================================================

        // 合并用户数据
        $userData = array_merge($row->toArray(), $profile);

        // 赋值到模板
        $this->view->assign("userData", $userData);
        $this->view->assign("financeData", $financeData ? $financeData->toArray() : []);
        $this->view->assign("stockValue", $stockValue);
        $this->view->assign("totalAssets", $totalAssets);

        return $this->view->fetch();
    }
    
    /**
     * Selectpage的实现方法
     * 通过两次独立查询（用户表、用户资料表）结合结果，显示"真实姓名(手机号)"
     */
    protected function selectpage()
    {
        // 基础过滤
        $this->request->filter(['trim', 'strip_tags', 'htmlspecialchars']);

        // 前端仅会传这几个参数
        $keywords   = (array)$this->request->request('q_word/a');
        $keyword    = isset($keywords[0]) ? trim($keywords[0]) : '';
        $page       = $this->request->request('pageNumber/d', 1);
        $pageSize   = $this->request->request('pageSize/d', 10);
        $keyValue   = $this->request->request('keyValue'); // 用于回显

        $userModel    = new EsopUserModel();
        $profileModel = new EsopUserProfileModel();

        // ----------------------- 回显模式 -----------------------
        if ($keyValue !== null) {
            $ids = array_filter(explode(',', $keyValue));
            if (empty($ids)) {
                return json(['list'=>[], 'total'=>0]);
            }
            // 回显模式不需要筛选条件
            $users = $userModel->where('id', 'in', $ids)->select();
            // 获取真实姓名，不需要筛选审核状态
            $profileMap = $profileModel->where('user_id', 'in', $ids)
                ->column('real_name','user_id');
            $list = [];
            foreach ($users as $u) {
                $real = isset($profileMap[$u['id']]) ? $profileMap[$u['id']] : '';
                $text = $real ? "$real ({$u['phone']})" : $u['phone'];
                $list[] = [
                    'id'       => $u['id'],
                    'nickname' => $text,
                ];
            }
            return json(['list'=>$list, 'total'=>count($list)]);
        }

        // ----------------------- 普通搜索模式 -----------------------
        // 修改：限定用户状态为正常，并通过联表查询限制只查询审核通过的用户
        $userQuery = $userModel->alias('u')
            ->join('esop_user_profiles p', 'u.id = p.user_id', 'LEFT')
            ->where('u.status', '1')
            ->where('p.audit_status', 1);

        if ($keyword !== '') {
            // 在手机号或真实姓名中搜索
            $userQuery->where(function ($query) use ($keyword) {
                $query->where('u.phone', 'like', "%{$keyword}%")
                      ->whereOr('p.real_name', 'like', "%{$keyword}%");
            });
        }

        $list  = [];
        $users = $userQuery->field('u.id,u.phone')->order('u.id','desc')->page($page,$pageSize)->paginate();
        if ($users->total() > 0) {
            // 修复：将paginator对象转换为数组再使用array_column
            $usersArray = $users->toArray();
            $ids = array_column($usersArray['data'], 'id');
            $profileMap = $profileModel->where('user_id','in',$ids)->column('real_name','user_id');
            foreach ($users as $u) {
                $real = isset($profileMap[$u['id']]) ? $profileMap[$u['id']] : '';
                $text = $real ? "$real ({$u['phone']})" : $u['phone'];
                $list[] = [
                    'id'       => $u['id'],
                    'nickname' => $text,
                ];
            }
        }
        return json(['list'=>$list, 'total'=>$users->total()]);
    }

    /**
     * 推荐人选择器接口
     */
    public function selectInviter()
    {
        // 设置过滤方法
        $this->request->filter(['trim', 'strip_tags', 'htmlspecialchars']);

        // 获取参数
        $word = (array)$this->request->request('q_word/a', []);
        $keyword = isset($word[0]) ? trim($word[0]) : '';
        $page = $this->request->request('pageNumber/d', 1);
        $pageSize = $this->request->request('pageSize/d', 10);
        $keyValue = $this->request->request('keyValue');

        $list = [];
        $total = 0;

        // 如果有keyValue，说明是回显模式
        if ($keyValue !== null) {
            $ids = array_filter(explode(',', $keyValue));
            if (!empty($ids)) {
                $users = $this->model->alias('u')
                    ->join('esop_user_profiles p', 'u.id = p.user_id', 'LEFT')
                    ->where('u.id', 'in', $ids)
                    ->field('u.id,u.phone,p.real_name')
                    ->paginate(['list_rows' => $pageSize, 'page' => $page]);

                $total = $users->total();

                foreach ($users as $u) {
                    $real = $u['real_name'] ?: '';
                    $text = $real ? "$real ({$u['phone']})" : $u['phone'];
                    $list[] = [
                        'id' => $u['id'],
                        'nickname' => $text,
                    ];
                }
            }
        } else {
            // 普通搜索模式
            $query = $this->model->alias('u')
                ->join('esop_user_profiles p', 'u.id = p.user_id', 'LEFT')
                ->where('u.status', '1');

            // 搜索条件
            if (!empty($keyword)) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('u.phone', 'like', "%{$keyword}%")
                      ->whereOr('p.real_name', 'like', "%{$keyword}%");
                });
            }


            // 获取分页数据
            $users = $query->field('u.id,u.phone,p.real_name')
                ->order('u.id', 'desc')
                ->paginate(['list_rows' => $pageSize, 'page' => $page]);

            $total = $users->total();

            foreach ($users as $u) {
                $real = $u['real_name'] ?: '';
                $text = $real ? "$real ({$u['phone']})" : $u['phone'];
                $list[] = [
                    'id' => $u['id'],
                    'nickname' => $text,
                ];
            }
        }

        return json(['list' => $list, 'total' => $total]);
    }
}