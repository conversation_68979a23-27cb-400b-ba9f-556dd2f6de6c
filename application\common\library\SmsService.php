<?php

namespace app\common\library;

use think\Config;
use think\Db;
use think\Log;

/**
 * 短信服务类
 */
class SmsService
{
    /**
     * 短信配置
     */
    protected $config = [];
    
    /**
     * 错误信息
     */
    protected $error = '';
    
    /**
     * 短信发送实例
     */
    protected $sender = null;
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 从数据库加载配置
        $this->loadConfig();
    }
    
    /**
     * 加载短信配置
     */
    protected function loadConfig()
    {
        $configs = Db::name('esop_third_party_configs')
            ->where('service_type', 'sms')
            ->where('provider', 'aliyun')
            ->select();
            
        foreach ($configs as $config) {
            $this->config[$config['config_key']] = $config['config_value'];
        }
        
        // 检查必要的配置是否存在
        $requiredKeys = ['accessKeyId', 'accessKeySecret', 'signName'];
        foreach ($requiredKeys as $key) {
            if (!isset($this->config[$key]) || empty($this->config[$key])) {
                Log::record("短信配置缺失: {$key}", 'error');
            }
        }
    }
    
    /**
     * 发送短信验证码
     *
     * @param string $mobile 手机号
     * @param string $code   验证码
     * @param string $event  事件类型 (login-登录, register-注册, changepwd-修改密码)
     * @return boolean
     */
    public function send($mobile, $code, $event = 'default')
    {
        if (empty($this->config['accessKeyId']) || empty($this->config['accessKeySecret'])) {
            $this->error = '短信配置不完整';
            return false;
        }
        
        try {
            // 优先尝试使用AliyunSms类（不依赖SDK）
            if ($this->sender === null) {
                $this->sender = new AliyunSms();
            }
            
            // 发送短信
            $result = $this->sender->send($mobile, $code, $event);
            if (!$result) {
                $this->error = $this->sender->getError();
                // 如果是特定错误，可以尝试其他方法
                if (strpos($this->error, '模板未配置') !== false) {
                    return $this->sendBySDK($mobile, $code, $event);
                }
                return false;
            }
            return true;
        } catch (\Exception $e) {
            // 如果有错误，尝试使用SDK方式发送
            $this->error = $e->getMessage();
            Log::record("短信发送异常: {$mobile}, {$this->error}", 'error');
            return $this->sendBySDK($mobile, $code, $event);
        }
    }
    
    /**
     * 通过阿里云SDK发送短信
     *
     * @param string $mobile 手机号
     * @param string $code   验证码
     * @param string $event  事件类型
     * @return boolean
     */
    protected function sendBySDK($mobile, $code, $event = 'default')
    {
        // 引入阿里云SDK
        $sdkPath = VENDOR_PATH . 'aliyun-php-sdk-core/Config.php';
        if (!file_exists($sdkPath)) {
            $this->error = '阿里云SDK未安装';
            Log::record("阿里云SDK未找到: {$sdkPath}", 'error');
            return false;
        }
        
        require_once $sdkPath;
        
        // 短信API产品名
        $product = "Dysmsapi";
        
        // 短信API产品域名
        $domain = "dysmsapi.aliyuncs.com";
        
        // 暂时不支持多Region
        $region = isset($this->config['regionId']) ? $this->config['regionId'] : "cn-hangzhou";
        
        try {
            // 由于是动态加载的SDK，使用字符串类名避免静态检查报错
            
            // 初始化Profile
            $profileClass = '\\DefaultProfile';
            $profile = call_user_func([$profileClass, 'getProfile'], 
                $region, $this->config['accessKeyId'], $this->config['accessKeySecret']);
            
            // 添加Endpoint
            call_user_func([$profileClass, 'addEndpoint'], $region, $region, $product, $domain);
            
            // 初始化Client
            $clientClass = '\\DefaultAcsClient';
            $acsClient = new $clientClass($profile);
            
            // 组装请求对象
            // 使用字符串动态创建类实例，避免静态检查错误
            $requestClass = '\\Dysmsapi\\Request\\V20170525\\SendSmsRequest';
            $request = new $requestClass();
            
            // 设置短信接收号码
            $request->setPhoneNumbers($mobile);
            
            // 设置签名名称
            $request->setSignName($this->config['signName']);
            
            // 获取模板CODE，优先查找特定事件模板
            $templateCode = $this->config['templateCode'] ?? '';
            $eventTemplateKey = 'templateCode_' . $event;
            if (isset($this->config[$eventTemplateKey])) {
                $templateCode = $this->config[$eventTemplateKey];
            }
            
            if (empty($templateCode)) {
                $this->error = '短信模板未配置';
                return false;
            }
            
            // 设置模板CODE
            $request->setTemplateCode($templateCode);
            
            // 设置模板参数
            $templateParam = [
                "code" => $code
            ];
            $request->setTemplateParam(json_encode($templateParam));
            
            // 发起访问请求
            $response = $acsClient->getAcsResponse($request);
            
            if ($response && $response->Code == 'OK') {
                return true;
            } else {
                $this->error = isset($response->Message) ? $response->Message : '发送失败';
                Log::record("短信发送失败: {$mobile}, " . json_encode($response, JSON_UNESCAPED_UNICODE), 'error');
                return false;
            }
        } catch (\Exception $e) {
            $this->error = $e->getMessage();
            Log::record("短信发送异常: {$mobile}, {$this->error}", 'error');
            return false;
        }
    }
    
    /**
     * 获取错误信息
     */
    public function getError()
    {
        return $this->error;
    }
} 