<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('规则名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rule_name" data-rule="required" class="form-control" name="row[rule_name]" type="text" value="{$row.rule_name|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('是否递减')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_decrease]', $isDecreaseList, $row.is_decrease)}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('规则类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[rule_type]', $ruleTypeList, $row.rule_type)}
        </div>
    </div>

    <div class="form-group" id="f-vesting_days" {if $row.rule_type == 2}style="display:none;"{/if}>
        <label class="control-label col-xs-12 col-sm-2">{:__('解禁天数')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-vesting_days" class="form-control" name="row[vesting_days]" type="number" min="1" value="{$row.vesting_days}">
        </div>
    </div>

    <div class="form-group" id="f-vesting_percentage" {if $row.rule_type == 1}style="display:none;"{/if}>
        <label class="control-label col-xs-12 col-sm-2">{:__('解禁比例')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-vesting_percentage" class="form-control" step="0.0001" name="row[vesting_percentage]" type="number" min="0" max="100" value="{$row.vesting_percentage}">
                <span class="input-group-addon">%</span>
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('执行类型')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[execution_type]', $executionTypeList, $row.execution_type)}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('执行时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-execution_time" class="form-control" name="row[execution_time]" type="time" value="{$row.execution_time}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', $statusList, $row.status)}
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 