define(['jquery','bootstrap','backend','table','form'],function($,undefined,Backend,Table,Form){
    var Controller={
        index:function(){
            Table.api.init({extend:{
                index_url:'vesting/rule/index'+location.search,
                add_url:'vesting/rule/add',
                edit_url:'vesting/rule/edit',
                // del_url:'vesting/rule/del', // 删除功能已移除
                multi_url:'vesting/rule/multi',
                table:'esop_vesting_rules'
            }});
            var table=$("#table");
            table.bootstrapTable({
                url:$.fn.bootstrapTable.defaults.extend.index_url,
                pk:'id',
                sortName:'id',
                columns:[[
                    {checkbox:true},
                    {field:'id',title:'ID',sortable:true},
                    {field:'rule_name',title:'规则名称',operate:'LIKE'},
                    {field:'is_decrease',title:'是否递减',searchList:{"0":"否","1":"是"},formatter:Table.api.formatter.normal},
                    {field:'rule_type_text',title:'规则类型',operate:false},
                    {field:'vesting_days',title:'解禁天数',operate:false},
                    {field:'vesting_percentage',title:'解禁比例',operate:false},
                    {field:'execution_type_text',title:'执行类型',operate:false},
                    {field:'execution_time',title:'执行时间',operate:false},
                    {field:'status_text',title:'状态',searchList:{"1":"启用","0":"禁用"},formatter:Table.api.formatter.status},
                    {field:'created_at',title:'创建时间',operate:'RANGE',addclass:'datetimerange',formatter:Table.api.formatter.datetime},
                    {field:'operate',title:'操作',table:table,events:Table.api.events.operate,formatter:Table.api.formatter.operate}
                ]]
            });
            Table.api.bindevent(table);
        },
        add:function(){Controller.api.bindevent();},
        edit:function(){Controller.api.bindevent();},
        api:{
            bindevent:function(){
                Form.api.bindevent($("form[role=form]"));
                // 规则类型选择联动
                $(document).on('change','input[name="row[rule_type]"]',function(){
                    var v=$(this).val();
                    if(v==1){
                        $('#f-vesting_days').show();
                        $('#f-vesting_percentage').hide();
                    }else{
                        $('#f-vesting_days').hide();
                        $('#f-vesting_percentage').show();
                    }
                });
            }
        }
    };return Controller;
}); 