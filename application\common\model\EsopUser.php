<?php

namespace app\common\model;

use think\Model;
use fast\Random;

/**
 * ESOP用户模型
 */
class EsopUser extends Model
{
    // 表名
    protected $name = 'esop_users';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    
    // 追加属性
    protected $append = [
        'gender_text'
    ];
    
    // 性别列表
    protected static $genderList = [
        'male' => '男',
        'female' => '女',
        'secret' => '保密'
    ];
    
    /**
     * 获取性别文本
     */
    public function getGenderTextAttr($value, $data)
    {
        return isset($data['gender']) ? self::$genderList[$data['gender']] : '';
    }
    
    /**
     * 获取器: 直接返回数据库中的datetime字符串
     * @param string|null $value 数据库中的datetime字符串
     * @return string|null
     */
    public function getLastLoginTimeAttr($value)
    {
        // 直接返回数据库中的datetime字符串
        return $value ?: null;
    }
    
    /**
     * 修改器: 确保存入数据库的是datetime字符串
     * @param int|string|null $value 时间戳或datetime字符串
     * @return string|null
     */
    public function setLastLoginTimeAttr($value)
    {
        // 如果传入的是时间戳，则转为datetime字符串，否则直接返回
        if (is_numeric($value)) {
            return date('Y-m-d H:i:s', $value);
        }
        return $value;
    }
    
    /**
     * 修改器: 密码加密
     */
    public function setPasswordAttr($value)
    {
        return $value ? password_hash($value, PASSWORD_DEFAULT) : '';
    }
    
    /**
     * 修改器: 确保头像有完整的URL
     */
    public function getAvatarAttr($value)
    {
        if (!$value) {
            return config('site.default_avatar');
        }
        
        if (strpos($value, 'http') !== 0) {
            return config('site.upload_url') . $value;
        }
        
        return $value;
    }
    
    /**
     * 通过ID获取未删除的用户
     * 
     * @param int $userId 用户ID
     * @return EsopUser|null
     */
    public static function getActiveUser($userId)
    {
        return self::where('id', $userId)
                ->find();
    }
    
    /**
     * 通过手机号获取用户
     * 
     * @param string $phone 手机号
     * @return EsopUser
     */
    public static function getByPhone($phone)
    {
        return self::where('phone', $phone)
                ->find();
    }
    
    /**
     * 重置密码
     * 
     * @param string $phone 手机号
     * @return string 新密码
     */
    public static function resetPassword($phone)
    {
        $user = self::getByPhone($phone);
        if (!$user) {
            return false;
        }
        $newPassword = Random::alnum(8);
        $user->password = $newPassword;
        $user->save();
        return $newPassword;
    }
    
    /**
     * 软删除用户
     * 
     * @param int $userId 用户ID
     * @return boolean
     */
    public static function softDelete($userId)
    {
        $user = self::get($userId);
        if (!$user) {
            return false;
        }
        return $user->delete() > 0;
    }
    
    /**
     * 检查用户实名认证状态
     * 
     * @param int $userId 用户ID
     * @return array 状态信息，包含is_verified(是否已认证)、audit_status(状态码)、status_text(状态文本)
     */
    public static function checkVerificationStatus($userId)
    {
        // 查询用户资料
        $profile = \app\common\model\EsopUserProfile::getByUserId($userId);
        
        if (!$profile) {
            // 未提交实名认证资料
            return [
                'is_verified' => false,
                'audit_status' => -1,
                'status_text' => '未提交实名认证资料'
            ];
        }
        
        // 根据审核状态返回不同状态信息
        switch ($profile['audit_status']) {
            case 0: // 审核中
                return [
                    'is_verified' => false,
                    'audit_status' => 0,
                    'status_text' => '实名认证审核中'
                ];
            case 1: // 审核通过
                return [
                    'is_verified' => true,
                    'audit_status' => 1,
                    'status_text' => '实名认证已通过'
                ];
            case 2: // 审核不通过
                return [
                    'is_verified' => false,
                    'audit_status' => 2,
                    'status_text' => '实名认证未通过'
                ];
            default: // 异常状态
                return [
                    'is_verified' => false,
                    'audit_status' => 99,
                    'status_text' => '实名认证状态异常'
                ];
        }
    }
} 