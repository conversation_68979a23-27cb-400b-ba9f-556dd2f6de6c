<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Stock code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-stock_code" data-rule="required" class="form-control" name="row[stock_code]" type="text" value="{$row.stock_code|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Stock name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-stock_name" data-rule="required" class="form-control" name="row[stock_name]" type="text" value="{$row.stock_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Stock unit')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-stock_unit" data-rule="required;digits" class="form-control" name="row[stock_unit]" type="number" step="1" min="1" value="{$row.stock_unit|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price type')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[price_type]', ['1'=>__('Average price'), '2'=>__('Agreement price')], $row['price_type'], ['data-toggle'=>'pricetype'])}
        </div>
    </div>
    <div class="form-group" id="average-time-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Average time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-average_time" data-rule="required;range(1~120)" class="form-control" name="row[average_time]" type="number" min="1" max="120" step="1" value="{$row.average_time|htmlentities}">
            <p class="help-block">单位：天，范围：1-120天（约4个月交易日）</p>
        </div>
    </div>
    <div class="form-group" id="stock-price-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Stock price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-stock_price" data-rule="required" class="form-control" step="0.0001" name="row[stock_price]" type="number" value="{$row.stock_price|htmlentities}">
                <span class="input-group-btn">
                    <button type="button" class="btn btn-success" id="btn-calculate-average-price">计算平均价格</button>
                    <button type="button" class="btn btn-info" id="btn-refresh-current-price">刷新最新价格</button>
                </span>
            </div>
            <p class="help-block" id="price-help-text">点击按钮根据股票代码和平均天数自动计算平均价格</p>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Exchangeable amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-exchangeable_amount" data-rule="required" class="form-control" step="0.0001" name="row[exchangeable_amount]" type="number" value="{$row.exchangeable_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is on shelf')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_on_shelf]', ['1'=>__('On shelf'), '0'=>__('Off shelf')], $row['is_on_shelf'])}
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Price history')}:</label>
        <div class="col-xs-12 col-sm-8">
            <table class="table table-striped table-bordered table-hover">
                <thead>
                    <tr>
                        <th>{:__('Id')}</th>
                        <th>{:__('Price type')}</th>
                        <th>{:__('Old price')}</th>
                        <th>{:__('New price')}</th>
                        <th>{:__('Set by')}</th>
                        <th>{:__('Set time')}</th>
                    </tr>
                </thead>
                <tbody>
                    {empty name="priceLogs"}
                    <tr>
                        <td colspan="6" class="text-center">{:__('No price history records')}</td>
                    </tr>
                    {else/}
                    {foreach name="priceLogs" item="item"}
                    <tr>
                        <td>{$item.id}</td>
                        <td>{$item.price_type == 1 ? __('Average price') : __('Agreement price')}</td>
                        <td>{$item.old_price}</td>
                        <td>{$item.new_price}</td>
                        <td>{$item.admin.username}</td>
                        <td>{:date('Y-m-d H:i:s', strtotime($item.set_time))}</td>
                    </tr>
                    {/foreach}
                    {/empty}
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 