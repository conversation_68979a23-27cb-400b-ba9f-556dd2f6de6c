<?php

namespace app\admin\validate;

use think\Validate;

class CustomerService extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'title' => 'require|max:20',
        'icon' => 'require',
        'account' => 'require|number'
    ];
    
    /**
     * 提示消息
     */
    protected $message = [
        'title.require' => '标题不能为空',
        'title.max' => '标题最多不能超过20个字符',
        'icon.require' => '图标不能为空',
        'account.require' => '账号不能为空',
        'account.number' => '账号必须为数字'
    ];
    
    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['title', 'icon', 'account'],
        'edit' => ['title', 'icon', 'account'],
    ];
} 