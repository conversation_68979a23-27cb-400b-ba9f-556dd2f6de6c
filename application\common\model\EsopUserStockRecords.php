<?php

namespace app\common\model;

use think\Model;
use think\Db;
use think\Exception;

/**
 * 用户股票记录模型
 *
 * 负责维护 `fa_esop_user_stock_records` 表的数据增减操作
 */
class EsopUserStockRecords extends Model
{
    // 表名，框架会自动加上表前缀
    protected $name = 'esop_user_stock_records';

    // 关闭自动时间戳，手动管理 created_at、updated_at
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 关联股票管理模型
     */
    public function stock()
    {
        return $this->belongsTo('EsopStockManagement', 'stock_id', 'id');
    }

    /**
     * 调整用户的股票数量（正数增加，负数减少）
     *
     * @param int   $userId   用户ID
     * @param int   $stockId  股票ID
     * @param mixed $amount   数量（支持字符串或数值，推荐字符串以避免精度问题）
     * @return int            受影响记录ID
     * @throws Exception      数量不足或数据库操作异常
     */
    public static function changeUserStock($userId, $stockId, $amount)
    {
        Db::startTrans();
        try {
            // 查询现有记录，不加锁，后续使用条件更新保障并发安全
            $where   = ['user_id' => $userId, 'stock_id' => $stockId];
            $record  = self::where($where)->find();
            $nowTime = date('Y-m-d H:i:s');

            // 如果记录已存在，走更新逻辑；否则走插入逻辑
            if ($record) {
                // ==================== 更新逻辑 ====================
                if (bccomp((string)$amount, '0', 8) < 0) {
                    // 减少数量，使用条件更新确保数量充足
                    $absValue    = ltrim((string)abs($amount), '+'); // 去掉正号，避免语法问题
                    $affectedRows = Db::name('esop_user_stock_records')
                        ->where('id', $record['id'])
                        ->where('amount', '>=', $absValue) // 保证数量足够
                        ->update([
                            // 数据库原子减，并确保不小于 0
                            'amount'     => Db::raw('GREATEST(0, amount - ' . $absValue . ')'),
                            'updated_at' => $nowTime,
                        ]);

                    if ($affectedRows === 0) {
                        throw new Exception('用户股票数量不足');
                    }
                } else {
                    // 增加数量，直接累加
                    $incValue = ltrim((string)$amount, '+');
                    Db::name('esop_user_stock_records')
                        ->where('id', $record['id'])
                        ->update([
                            'amount'     => Db::raw('amount + ' . $incValue),
                            'updated_at' => $nowTime,
                        ]);
                }

                $recordId = $record['id'];
                // =================================================
            } else {
                // ==================== 插入逻辑 ====================
                // 新增记录，数量必须为正
                if (bccomp((string)$amount, '0', 8) <= 0) {
                    throw new Exception('新增股票数量必须大于0');
                }

                $recordId = Db::name('esop_user_stock_records')->insertGetId([
                    'user_id'    => $userId,
                    'stock_id'   => $stockId,
                    'amount'     => $amount,
                    'created_at' => $nowTime,
                    'updated_at' => $nowTime,
                ]);
                // =================================================
            }

            Db::commit();
            return $recordId;
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
} 