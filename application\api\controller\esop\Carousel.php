<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopCarouselImages;

/**
 * 轮播图接口
 */
class Carousel extends Api
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = '*';
    
    /**
     * 获取轮播图列表
     * 
     * @ApiTitle    (获取轮播图列表)
     * @ApiSummary  (获取系统中所有启用的轮播图列表，支持分页)
     * @ApiMethod   (GET)
     * @ApiParams   (name="page", type="integer", required=false, description="页码，默认为1")
     * @ApiParams   (name="limit", type="integer", required=false, description="每页记录数，默认为10")
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":"1688888888","data":{"total":3,"per_page":10,"current_page":1,"last_page":1,"data":[{"id":1,"image_url":"http://example.com/image1.jpg","link_url":"http://example.com","sort_order":100}]}})
     */
    public function index()
    {
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 10);
        
        // 查询所有启用状态的轮播图
        $query = EsopCarouselImages::where('status', 1);
        
        // 分页查询数据
        $list = $query->order('sort_order', 'asc')
            ->paginate($limit, false, ['page' => $page]);
        
        // 处理图片URL
        foreach ($list as &$item) {
            if (!empty($item['image_url'])) {
                $item['image_url'] = cdnurl($item['image_url'], true);
            }
        }
        
        // 构建分页数据
        $result = [
            'total' => $list->total(),
            'per_page' => $limit,
            'current_page' => $page,
            'last_page' => $list->lastPage(),
            'rows' => $list->items()
        ];
        
        return $this->success('获取成功', $result);
    }
} 