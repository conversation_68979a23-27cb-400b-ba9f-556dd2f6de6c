<?php

namespace app\common\model;

use think\Model;
use think\Db;
use think\Exception;

/**
 * B端账户模型
 */
class EsopBAccount extends Model
{
    // 表名
    protected $name = 'esop_b_accounts';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 开启乐观锁
    protected $optimLock = 'version';

    // 追加属性
    protected $append = [
        'ui_style_text',
        'ui_style_color',
    ];
    
    /**
     * 关联多个用户表（通过user_ids字段，逗号分隔）
     * @return array 用户模型数组
     */
    public function users()
    {
        // 获取用户ID数组
        $userIds = $this->getUserIdsArray();
        if (empty($userIds)) {
            return [];
        }
        // 查询所有未删除的用户
        return \app\common\model\EsopUser::where('id', 'in', $userIds)
            ->select();
    }

    /**
     * 获取user_ids字段的用户ID数组
     * @return array 用户ID数组
     */
    public function getUserIdsArray()
    {
        if (empty($this->user_ids)) {
            return [];
        }
        return explode(',', $this->user_ids);
    }

    /**
     * 获取所有用户的真实姓名+手机号信息
     * @return array 形如 [['real_name'=>'张三','phone'=>'138****8888'], ...]
     */
    public function getUsersInfo()
    {
        $users = $this->users();
        $result = [];
        foreach ($users as $user) {
            // 获取用户真实姓名
            $profile = \app\common\model\EsopUserProfile::getByUserId($user->id);
            $realName = $profile ? $profile->real_name : '';
            $result[] = [
                'real_name' => $realName,
                'phone'     => $user->phone
            ];
        }
        return $result;
    }
    
    /**
     * 获取解禁规则列表
     */
    public function getVestingRules()
    {
        // 如果vesting_rule_ids为空，返回空数组
        if (empty($this->vesting_rule_ids)) {
            return [];
        }
        
        // 分割ID字符串，获取所有规则ID
        $ruleIds = explode(',', $this->vesting_rule_ids);
        
        // 查询这些ID对应的规则
        return \app\common\model\EsopVestingRule::where('id', 'in', $ruleIds)
            ->select();
    }
    
    /**
     * 获取规则ID数组
     */
    public function getVestingRuleIdsArray()
    {
        if (empty($this->vesting_rule_ids)) {
            return [];
        }
        return explode(',', $this->vesting_rule_ids);
    }
    
    /**
     * 获取UI风格文本
     */
    public function getUiStyleTextAttr($value, $data)
    {
        if (isset($data['ui_style_id'])) {
            // 从UI风格表中获取名称
            $uiStyle = \app\common\model\UiStyle::where('id', $data['ui_style_id'])
                ->value('style_name');
                
            return $uiStyle ?: '';
        }
        return '';
    }

    /**
     * 获取UI风格颜色
     */
    public function getUiStyleColorAttr($value, $data)
    {
        return $data['ui_style_id'] ? \app\common\model\UiStyle::where('id', $data['ui_style_id'])->value('main_color') : '';
    }
    
    /**
     * 获取团队成员数量
     */
    public function getTeamMemberCount()
    {
        // 统计邀请关系表里当前 B 端账户ID 对应的成员数量
        return \app\common\model\EsopInvitationRelation::where('b_account_id', $this->id)
            ->count();
    }
    
    /**
     * 停用B端账户
     */
    public function disable()
    {
        // 实际业务中可能需要更多的逻辑处理
        $this->status = 0;
        return $this->save();
    }
    
    /**
     * 启用B端账户
     */
    public function enable()
    {
        // 实际业务中可能需要更多的逻辑处理
        $this->status = 1;
        return $this->save();
    }
    
    /**
     * 更新B端账户资产（使用条件更新确保资产安全）
     * 
     * @param int $accountId B端账户ID
     * @param array $changes 资产变更数据，如 ['available_assets' => -100, 'invested_assets' => 100]
     * @return bool 更新结果
     * @throws \think\Exception 更新失败异常
     */
    public static function updateAccountAsset($accountId, $changes)
    {
        \think\Db::startTrans();
        try {
            // 先检查账户是否存在
            $account = self::where('id', $accountId)->find();
            if (!$account) {
                throw new \think\Exception('B端账户不存在');
            }
            
            // 记录是否有更新
            $updated = false;
            
            // 处理每个资产变更
            foreach ($changes as $field => $value) {
                if (isset($account[$field])) {
                    // 处理高精度数值
                    if (is_string($value) && is_numeric($value) && strpos($field, 'assets') !== false) {
                        // 对减少操作使用条件更新
                        if (bccomp($value, '0', 8) < 0) {
                            // 获取绝对值用于比较
                            $absValue = bcmul($value, '-1', 8);
                            
                            // 使用条件更新确保余额充足
                            $affectedRows = Db::name('esop_b_accounts')
                                ->where('id', $accountId)
                                ->where($field, '>=', $absValue) // 确保余额充足
                                ->update([
                                    // 使用减法避免'+-'语法错误
                                    $field => Db::raw('ROUND(' . $field . ' - ' . $absValue . ', 8)'),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'version' => Db::raw('version+1'),
                                ]);
                            
                            if ($affectedRows == 0) {
                                throw new \think\Exception($field . '资产不足');
                            }
                        } else {
                            // 增加操作可以直接更新
                            Db::name('esop_b_accounts')
                                ->where('id', $accountId)
                                ->update([
                                    $field => Db::raw('ROUND(' . $field . '+' . $value . ', 8)'),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'version' => Db::raw('version+1'),
                                ]);
                        }
                        $updated = true;
                    } else {
                        // 处理普通数值
                        if ($value < 0) {
                            // 对减少操作使用条件更新
                            $absValueInt = abs($value);
                            $affectedRows = Db::name('esop_b_accounts')
                                ->where('id', $accountId)
                                ->where($field, '>=', $absValueInt) // 确保余额充足
                                ->update([
                                    // 使用减法表达式，避免'+-'
                                    $field => Db::raw($field . ' - ' . $absValueInt),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'version' => Db::raw('version+1'),
                                ]);
                            
                            if ($affectedRows == 0) {
                                throw new \think\Exception($field . '资产不足');
                            }
                        } else {
                            // 增加操作可以直接更新
                            Db::name('esop_b_accounts')
                                ->where('id', $accountId)
                                ->update([
                                    $field => Db::raw($field . '+' . $value),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'version' => Db::raw('version+1'),
                                ]);
                        }
                        $updated = true;
                    }
                }
            }
            
            // 如果没有任何字段被更新，也算成功
            \think\Db::commit();
            return true;
        } catch (\Exception $e) {
            \think\Db::rollback();
            throw $e;
        }
    }
}