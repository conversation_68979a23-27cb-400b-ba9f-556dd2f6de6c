<?php

namespace app\admin\validate;

use think\Validate;

class Carousel extends Validate
{
    protected $rule = [
        'image_url' => 'require|url',
        'link_url' => 'require|url',
        'title' => 'require|max:255',
        'subtitle' => 'require|max:255',
        'sort_order' => 'require|integer|egt:0',
        'status' => 'require|in:0,1',
    ];

    protected $message = [
        'image_url.require' => '图片URL不能为空',
        'image_url.url' => '图片URL格式不正确',
        'link_url.require' => '链接URL不能为空',
        'link_url.url' => '链接URL格式不正确',
        'title.require' => '标题不能为空',
        'title.max' => '标题长度不能超过255个字符',
        'subtitle.require' => '副标题不能为空',
        'subtitle.max' => '副标题长度不能超过255个字符',
        'sort_order.require' => '排序值不能为空',
        'sort_order.integer' => '排序值必须为整数',
        'sort_order.egt' => '排序值必须大于等于0',
        'status.require' => '状态不能为空',
        'status.in' => '状态值无效',
    ];
} 