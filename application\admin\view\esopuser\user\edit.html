<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('手机号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" data-rule="required" class="form-control" name="row[phone]" type="text" value="{$row.phone|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('密码')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" class="form-control" name="row[password]" type="password" placeholder="{:__('不修改密码请留空')}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('昵称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-nickname" class="form-control" name="row[nickname]" type="text" value="{$row.nickname|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('真实姓名')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-real_name" class="form-control" name="row[real_name]" type="text" value="{$row.real_name|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('性别')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[gender]', ['0'=>__('保密'), '1'=>__('男'), '2'=>__('女')], $row.gender)}
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('出生日期')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-birth_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[birth_date]" type="text" value="{$row.birth_date}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('身份证号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-id_card_no" class="form-control" name="row[id_card_no]" type="text" value="{$row.id_card_no|htmlentities}">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('推荐人')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-inviter_id" data-source="esopuser/user/selectInviter" data-field="nickname" data-primary-key="id" data-order-by="id desc" class="form-control selectpage" name="row[inviter_id]" type="text" value="{$row.inviter_id|default=''}">
            <div class="help-block">当前推荐人：{$row.inviter_display|default='无'}</div>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('婚姻状况')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[marital_status]', $maritalStatusList, $row.marital_status, ['class'=>'form-control selectpicker'])}
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('证券账户')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-securities_account" class="form-control" name="row[securities_account]" type="text" value="{$row.securities_account|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('开户行')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_name" class="form-control" name="row[bank_name]" type="text" value="{$row.bank_name|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('所属支行')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_branch" class="form-control" name="row[bank_branch]" type="text" value="{$row.bank_branch|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('银行卡号')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_account" class="form-control" name="row[bank_account]" type="text" value="{$row.bank_account|htmlentities}">
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('银行卡图片')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-bank_image" class="form-control" size="50" name="row[bank_image]" type="text" value="{$row.bank_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-bank_image" class="btn btn-danger plupload" data-input-id="c-bank_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-bank_image"><i class="fa fa-upload"></i> {:__('上传')}</button></span>
                    <span><button type="button" id="fachoose-bank_image" class="btn btn-primary fachoose" data-input-id="c-bank_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('选择')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-bank_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-bank_image">
                {if $row.bank_image}
                <li><a href="{$row.bank_image}" target="_blank"><img src="{$row.bank_image}" class="img-responsive"></a></li>
                {/if}
            </ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('身份证正面')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-front_image" class="form-control" size="50" name="row[front_image]" type="text" value="{$row.front_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-front_image" class="btn btn-danger plupload" data-input-id="c-front_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-front_image"><i class="fa fa-upload"></i> {:__('上传')}</button></span>
                    <span><button type="button" id="fachoose-front_image" class="btn btn-primary fachoose" data-input-id="c-front_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('选择')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-front_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-front_image">
                {if $row.front_image}
                <li><a href="{$row.front_image}" target="_blank"><img src="{$row.front_image}" class="img-responsive"></a></li>
                {/if}
            </ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('身份证背面')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-back_image" class="form-control" size="50" name="row[back_image]" type="text" value="{$row.back_image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-back_image" class="btn btn-danger plupload" data-input-id="c-back_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-back_image"><i class="fa fa-upload"></i> {:__('上传')}</button></span>
                    <span><button type="button" id="fachoose-back_image" class="btn btn-primary fachoose" data-input-id="c-back_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('选择')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-back_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-back_image">
                {if $row.back_image}
                <li><a href="{$row.back_image}" target="_blank"><img src="{$row.back_image}" class="img-responsive"></a></li>
                {/if}
            </ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('审核状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[audit_status]', $auditStatusList, $row.audit_status, ['class'=>'form-control selectpicker'])}
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('正常'), '0'=>__('禁用')], $row.status)}
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('确定')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('重置')}</button>
        </div>
    </div>
</form> 