<?php

namespace app\common\model;

use think\Model;

/**
 * ESOP身份证图片模型
 */
class EsopIdCardImage extends Model
{
    // 表名
    protected $name = 'esop_id_card_images';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'verification_status_text'
    ];
    
    // 验证状态列表
    protected static $verificationStatusList = [
        0 => '未验证',
        1 => '已验证'
    ];
    
    /**
     * 获取验证状态文本
     */
    public function getVerificationStatusTextAttr($value, $data)
    {
        return isset($data['is_verified']) ? self::$verificationStatusList[$data['is_verified']] : '';
    }
    
    /**
     * 修改器：确保图片URL有完整路径
     */
    public function getFrontImageAttr($value)
    {
        if (!$value) {
            return '';
        }
        
        if (strpos($value, 'http') !== 0) {
            return config('site.upload_url') . $value;
        }
        
        return $value;
    }
    
    /**
     * 修改器：确保图片URL有完整路径
     */
    public function getBackImageAttr($value)
    {
        if (!$value) {
            return '';
        }
        
        if (strpos($value, 'http') !== 0) {
            return config('site.upload_url') . $value;
        }
        
        return $value;
    }
    
    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }
    
    /**
     * 根据用户ID获取身份证图片
     * 
     * @param int $userId 用户ID
     * @return EsopIdCardImage|null
     */
    public static function getByUserId($userId)
    {
        return self::where('user_id', $userId)
                ->find();
    }
    
    /**
     * 创建或更新身份证图片
     * 
     * @param int $userId 用户ID
     * @param string $frontImage 身份证正面图片
     * @param string $backImage 身份证反面图片
     * @return EsopIdCardImage
     */
    public static function createOrUpdate($userId, $frontImage, $backImage)
    {
        $idCardImage = self::getByUserId($userId);
        
        if (!$idCardImage) {
            // 创建新记录
            $idCardImage = new self();
            $idCardImage->user_id = $userId;
            $idCardImage->is_verified = 0; // 默认未验证
        }
        
        // 更新图片
        if ($frontImage) {
            $idCardImage->front_image = $frontImage;
        }
        if ($backImage) {
            $idCardImage->back_image = $backImage;
        }
        
        // 更新上传时间
        $idCardImage->upload_time = date('Y-m-d H:i:s');
        
        $idCardImage->save();
        return $idCardImage;
    }
    
    /**
     * 更新验证状态
     * 
     * @param int $userId 用户ID
     * @param int $status 验证状态，0-未验证，1-已验证
     * @return boolean
     */
    public static function updateVerificationStatus($userId, $status)
    {
        $idCardImage = self::getByUserId($userId);
        if (!$idCardImage) {
            return false;
        }
        
        $idCardImage->is_verified = $status;
        return $idCardImage->save();
    }
    
    /**
     * 软删除身份证图片
     * 
     * @param int $userId 用户ID
     * @return boolean
     */
    public static function softDeleteByUserId($userId)
    {
        $idCardImage = self::getByUserId($userId);
        if (!$idCardImage) {
            return false;
        }
        
        return $idCardImage->delete() > 0;
    }
} 