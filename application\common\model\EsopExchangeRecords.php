<?php

namespace app\common\model;

use think\Model;

/**
 * 换股记录模型
 */
class EsopExchangeRecords extends Model
{
    // 表名
    protected $name = 'esop_exchange_records';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    
    // 追加属性
    protected $append = [
        'stock_price_type_text'
    ];
    
    // 定义价格类型获取器
    public function getStockPriceTypeTextAttr($value, $data)
    {
        $priceTypes = [
            1 => __('Average price'),
            2 => __('Agreement price')
        ];
        return isset($priceTypes[$data['stock_price_type']]) ? $priceTypes[$data['stock_price_type']] : '';
    }
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }
    
    // 关联股票
    public function stock()
    {
        return $this->belongsTo('EsopStockManagement', 'stock_id', 'id');
    }
} 