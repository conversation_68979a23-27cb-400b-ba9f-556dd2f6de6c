<?php

namespace app\common\model;

use think\Model;

/**
 * 第三方配置模型
 */
class EsopThirdPartyConfig extends Model
{
    // 表名
    protected $name = 'esop_third_party_configs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 批量保存配置
     * 
     * @param array $configs 配置数组
     * @param string $serviceType 服务类型
     * @param string $provider 提供商
     * @return boolean
     */
    public static function saveConfigs($configs, $serviceType = 'sms', $provider = 'aliyun')
    {
        if (!$configs) {
            return false;
        }
        
        foreach ($configs as $key => $value) {
            // 查询是否存在该配置
            $exists = self::where([
                'service_type' => $serviceType,
                'provider' => $provider,
                'config_key' => $key
            ])->find();
            
            if ($exists) {
                // 更新
                $exists->config_value = $value;
                $exists->save();
            } else if (!empty($value)) {
                // 新增
                self::create([
                    'service_type' => $serviceType,
                    'provider' => $provider,
                    'config_key' => $key,
                    'config_value' => $value
                ]);
            }
        }
        
        return true;
    }
    
    /**
     * 获取配置
     * 
     * @param string $serviceType 服务类型
     * @param string $provider 提供商
     * @return array
     */
    public static function getConfigs($serviceType = null, $provider = null)
    {
        $where = [];
        if ($serviceType) {
            $where['service_type'] = $serviceType;
        }
        if ($provider) {
            $where['provider'] = $provider;
        }
        
        $configs = self::where($where)->select();
        
        $result = [];
        foreach ($configs as $config) {
            if (!isset($result[$config['service_type']])) {
                $result[$config['service_type']] = [];
            }
            
            $result[$config['service_type']][$config['config_key']] = $config['config_value'];
        }
        
        return $result;
    }
} 