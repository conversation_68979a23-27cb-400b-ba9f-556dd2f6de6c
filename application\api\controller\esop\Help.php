<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopArticleCategory as EsopArticleCategoryModel;
use app\common\model\EsopArticle as EsopArticleModel;

/**
 * 帮助中心接口
 */
class Help extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';
    
    
    /**
     * 获取文章详情
     *
     * @param int $id 文章ID
     */
    public function detail()
    {
        $id = $this->request->get('id');
        
        if (!$id) {
            return $this->toError('参数错误');
        }
        
        $article = EsopArticleModel::with(['category', 'baccount'])
            ->where('id', $id)
            ->where('status', 1)
            ->find();
            
        if (!$article) {
            return $this->toError('文章不存在或已下架');
        }
        
        $article->save();
        
        $data = [
            'id' => $article->id,
            'title' => $article->title,
            'content' => $article->content,
            'summary' => $article->summary,
            'cover_image' => $article->cover_image,
            'is_top' => $article->is_top,
            'created_at' => $article->created_at,
            'updated_at' => $article->updated_at,
            'category' => $article->category ? [
                'id' => $article->category->id,
                'name' => $article->category->category_name,
            ] : null,
            'b_account' => $article->baccount ? [
                'id' => $article->baccount->id,
                'name' => $article->baccount->name
            ] : null
        ];
        
        return $this->jsonSucess('获取成功', $data);
    }
    
    /**
     * 搜索文章
     *
     * @param string $keyword     搜索关键词
     * @param int $category_id    分类ID (可选)
     * @param int $b_account_id   B端账户ID (可选)
     * @param int $page           页码，默认1
     * @param int $limit          每页数量，默认10
     */
    public function search()
    {
        $keyword = $this->request->param('keyword');
        $categoryId = $this->request->param('category_id', 0);
        $bAccountId = $this->request->param('b_account_id', 0);

        if (!$keyword) {
            return $this->toError('请输入搜索关键词');
        }

        // 1. 获取所有分类
        $catWhere = [];
        if ($bAccountId > 0) {
            $catWhere['b_account_id'] = $bAccountId;
        }
        $categories = EsopArticleCategoryModel::where($catWhere)->order('id ASC')->select();

        // 2. 获取所有匹配文章
        $artWhere = [
            'status' => 1
        ];
        if ($categoryId > 0) {
            $artWhere['category_id'] = $categoryId;
        }
        if ($bAccountId > 0) {
            $artWhere['b_account_id'] = $bAccountId;
        }
        // 多字段模糊搜索
        $articles = EsopArticleModel::where($artWhere)
            ->where(function($query) use ($keyword) {
                $query->where('title', 'like', "%{$keyword}%")
                ->whereOr('content', 'like', "%{$keyword}%")
                ->whereOr('summary', 'like', "%{$keyword}%");
            })
            ->order('is_top DESC, sort_order ASC, created_at DESC')
            ->select();

        // 3. 按分类分组
        $grouped = [];
        foreach ($categories as $category) {
            $list = [];
            foreach ($articles as $article) {
                if ($article->category_id == $category->id) {
                    $list[] = [
                        'id' => $article->id,
                        'title' => $article->title
                    ];
                }
            }
            if ($list) {
                $grouped[] = [
                    'id' => $category->id,
                    'name' => $category->category_name,
                    'articles' => $list
                ];
            }
        }

        return $this->jsonSucess('搜索成功', $grouped);
    }
    
    /**
     * 获取分类及其下文章列表（分组结构，适配小程序帮助中心页面）
     * @param int $b_account_id B端账户ID (可选)
     * @return \think\Response
     */
    public function index()
    {
        $bAccountId = $this->request->get('b_account_id', 0);
        $where = [
           'board_id' => 0
        ];
        if ($bAccountId > 0) {
            $where['b_account_id'] = $bAccountId;
        }
        // 获取所有分类
        $categories = EsopArticleCategoryModel::where($where)
            ->order('id ASC')
            ->select();
        $result = [];
        foreach ($categories as $category) {
            // 获取该分类下的所有已发布文章
            $articles = EsopArticleModel::where([
                'category_id' => $category->id,
                'status' => 1,
                'board_id' => 0
            ])->order('is_top DESC, sort_order ASC, created_at DESC')->select();
            $articleList = [];
            foreach ($articles as $article) {
                $articleList[] = [
                    'id' => $article->id,
                    'title' => $article->title
                ];
            }
            $result[] = [
                'id' => $category->id,
                'name' => $category->category_name,
                'articles' => $articleList
            ];
        }
        return $this->jsonSucess('获取成功', $result);
    }
} 