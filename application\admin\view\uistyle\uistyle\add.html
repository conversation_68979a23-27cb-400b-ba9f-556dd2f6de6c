<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Style_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-style_name" class="form-control" name="row[style_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Main_color')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-main_color" class="form-control" name="row[main_color]" type="text" value="#000000">
                <span class="input-group-addon"><i></i></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Home_banner_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-home_banner_url" class="form-control" size="50" name="row[home_banner_url]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-home_banner_url" class="btn btn-danger plupload" data-input-id="c-home_banner_url" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-home_banner_url"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-home_banner_url" class="btn btn-primary fachoose" data-input-id="c-home_banner_url" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-home_banner_url"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-home_banner_url"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Inner_banner_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-inner_banner_url" class="form-control" size="50" name="row[inner_banner_url]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-inner_banner_url" class="btn btn-danger plupload" data-input-id="c-inner_banner_url" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-inner_banner_url"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-inner_banner_url" class="btn btn-primary fachoose" data-input-id="c-inner_banner_url" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-inner_banner_url"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-inner_banner_url"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Member_banner_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-member_banner_url" class="form-control" size="50" name="row[member_banner_url]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-member_banner_url" class="btn btn-danger plupload" data-input-id="c-member_banner_url" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-member_banner_url"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-member_banner_url" class="btn btn-primary fachoose" data-input-id="c-member_banner_url" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-member_banner_url"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-member_banner_url"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_default')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_default]', ['0'=>__('Is_default 0'), '1'=>__('Is_default 1')])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 