<?php

namespace app\common\model;

use think\Model;
use think\Db;

/**
 * 转出记录模型
 */
class EsopTransferRecord extends Model
{
    // 表名
    protected $name = 'esop_transfer_records';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    /**
     * 关联转出用户
     */
    public function fromUser()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id')->setEagerlyType(0);
    }
    
    /**
     * 关联目标用户
     */
    public function toUser()
    {
        return $this->belongsTo('EsopUser', 'target_user_id', 'id')->setEagerlyType(0);
    }
} 