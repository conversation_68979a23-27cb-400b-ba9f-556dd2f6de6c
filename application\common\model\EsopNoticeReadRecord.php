<?php
namespace app\common\model;

use think\Model;

/**
 * 公告已读记录模型
 */
class EsopNoticeReadRecord extends Model
{
    protected $name = 'esop_notice_read_records';

    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }

    /**
     * 关联公告
     */
    public function notice()
    {
        return $this->belongsTo('EsopArticle', 'notice_id', 'id');
    }

    /**
     * 添加已读记录
     * 
     * @param int $userId 用户ID
     * @param int $noticeId 公告ID
     * @return boolean
     */
    public static function addRecord($userId, $noticeId)
    {
        // 检查是否已存在记录
        $exists = self::where('user_id', $userId)
            ->where('notice_id', $noticeId)
            ->find();

        if ($exists) {
            return true; // 已存在记录，直接返回成功
        }

        // 创建新记录
        $record = new self();
        $record->user_id = $userId;
        $record->notice_id = $noticeId;
        $record->created_at = date('Y-m-d H:i:s');
        return $record->save();
    }

    /**
     * 获取用户已读公告ID列表
     * 
     * @param int $userId 用户ID
     * @return array 已读公告ID数组
     */
    public static function getReadIds($userId)
    {
        return self::where('user_id', $userId)
            ->column('notice_id');
    }
} 