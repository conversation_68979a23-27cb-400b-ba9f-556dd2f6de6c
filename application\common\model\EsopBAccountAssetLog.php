<?php

namespace app\common\model;

use think\Model;

/**
 * B端账户资产变化日志模型
 */
class EsopBAccountAssetLog extends Model
{
    // 表名
    protected $name = 'esop_b_account_asset_logs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    
    // 追加属性
    protected $append = [
        
    ];
    
    /**
     * 记录B端账户资产变动日志
     * 
     * @param int $bAccountId B端账户ID
     * @param int $changeType 变更类型：1-增加，2-减少
     * @param float $changeAmount 变更金额
     * @param float $beforeAmount 变更前金额
     * @param float $afterAmount 变更后金额
     * @param string $operationNotes 操作备注
     * @param int $operatorId 操作人ID
     * @return EsopBAccountAssetLog
     */
    public static function recordAssetChange($bAccountId, $changeType, $changeAmount, $beforeAmount, $afterAmount, 
                                           $operationNotes = '', $operatorId = null)
    {
        $log = new self();
        $log->b_account_id = $bAccountId;
        $log->change_type = $changeType;
        $log->change_amount = $changeAmount;
        $log->before_amount = $beforeAmount;
        $log->after_amount = $afterAmount;
        $log->operation_notes = $operationNotes;
        $log->operator_id = $operatorId;
        $log->created_at = date('Y-m-d H:i:s');
        $log->save();
        
        return $log;
    }
} 