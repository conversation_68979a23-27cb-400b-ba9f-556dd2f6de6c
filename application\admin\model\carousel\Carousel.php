<?php

namespace app\admin\model\carousel;

use think\Model;

/**
 * 轮播图模型
 */
class Carousel extends Model
{
    // 表名
    protected $name = 'esop_carousel_images';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [
        'status_text',
    ];

    /**
     * 获取状态列表
     * @return array
     */
    public static function getStatusList()
    {
        return ['0' => __('禁用'), '1' => __('启用')];
    }

    /**
     * 获取状态文本
     * @return mixed
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
} 