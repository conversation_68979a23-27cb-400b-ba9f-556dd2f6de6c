<?php

namespace app\common\library;

use think\Config;
use think\Db;
use think\Log;

/**
 * 阿里云短信服务类（简化版）
 */
class AliyunSms
{
    /**
     * 短信配置
     */
    protected $config = [];
    
    /**
     * 短信模板配置
     */
    protected $templates = [];
    
    /**
     * 错误信息
     */
    protected $error = '';
    
    /**
     * API域名
     */
    protected $domain = 'dysmsapi.aliyuncs.com';
    
    /**
     * 构造函数
     */
    public function __construct()
    {
        // 从数据库加载配置
        $this->loadConfig();
    }
    
    /**
     * 加载短信配置
     */
    protected function loadConfig()
    {
        $configs = Db::name('esop_third_party_configs')
            ->where('service_type', 'sms')
            ->where('provider', 'aliyun')
            ->select();
            
        foreach ($configs as $config) {
            $this->config[$config['config_key']] = $config['config_value'];
            
            // 处理模板配置
            if (strpos($config['config_key'], 'templateCode_') === 0) {
                $event = str_replace('templateCode_', '', $config['config_key']);
                $this->templates[$event] = $config['config_value'];
            }
        }
        
        // 检查必要的配置是否存在
        $requiredKeys = ['accessKeyId', 'accessKeySecret', 'signName'];
        foreach ($requiredKeys as $key) {
            if (!isset($this->config[$key]) || empty($this->config[$key])) {
                Log::record("短信配置缺失: {$key}", 'error');
            }
        }
        
        // 如果没有特定模板，则使用默认模板
        if (empty($this->templates) && isset($this->config['templateCode'])) {
            $this->templates['default'] = $this->config['templateCode'];
        }
    }
    
    /**
     * 发送短信验证码
     *
     * @param string $mobile 手机号
     * @param string $code   验证码
     * @param string $event  事件类型 (login-登录, register-注册, changepwd-修改密码)
     * @return boolean
     */
    public function send($mobile, $code, $event = 'default')
    {
        if (empty($this->config['accessKeyId']) || empty($this->config['accessKeySecret'])) {
            $this->error = '短信配置不完整';
            return false;
        }
        
        // 获取对应事件的模板代码，如果没有则使用默认模板
        $templateCode = isset($this->templates[$event]) ? 
                        $this->templates[$event] : 
                        (isset($this->templates['default']) ? $this->templates['default'] : '');
                        
        if (empty($templateCode)) {
            $this->error = "短信模板未配置: {$event}";
            Log::record($this->error, 'error');
            return false;
        }
        
        $params = [
            'AccessKeyId' => $this->config['accessKeyId'],
            'Action' => 'SendSms',
            'Format' => 'JSON',
            'SignatureMethod' => 'HMAC-SHA1',
            'SignatureNonce' => uniqid(),
            'SignatureVersion' => '1.0',
            'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
            'Version' => '2017-05-25',
            'PhoneNumbers' => $mobile,
            'SignName' => $this->config['signName'],
            'TemplateCode' => $templateCode,
            'TemplateParam' => json_encode(['code' => $code])
        ];
        
        // 对参数进行排序
        ksort($params);
        
        // 构造签名字符串
        $queryString = '';
        foreach ($params as $key => $value) {
            $queryString .= '&' . $this->percentEncode($key) . '=' . $this->percentEncode($value);
        }
        $queryString = substr($queryString, 1);
        
        // 构造待签名字符串
        $stringToSign = 'GET&' . $this->percentEncode('/') . '&' . $this->percentEncode($queryString);
        
        // 计算签名
        $signature = base64_encode(hash_hmac('sha1', $stringToSign, $this->config['accessKeySecret'] . '&', true));
        
        // 将签名加入参数
        $params['Signature'] = $signature;
        
        // 构造请求URL
        $url = 'https://' . $this->domain . '/?';
        $queryParams = [];
        foreach ($params as $key => $value) {
            $queryParams[] = $this->percentEncode($key) . "=" . $this->percentEncode($value);
        }
        $url .= implode('&', $queryParams);

        // 发送请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        curl_close($ch);
        
        // 处理响应
        if ($error) {
            $this->error = "cURL Error: " . $error;
            Log::record("短信发送cURL错误: {$mobile}, {$this->error}", 'error');
            return false;
        }
        
        $result = json_decode($response, true);
        if ($result && isset($result['Code']) && $result['Code'] === 'OK') {
            return true;
        } else {
            $this->error = isset($result['Message']) ? $result['Message'] : '发送失败';
            Log::record("短信发送失败: {$mobile}, " . json_encode($result, JSON_UNESCAPED_UNICODE), 'error');
            return false;
        }
    }
    
    /**
     * 获取错误信息
     */
    public function getError()
    {
        return $this->error;
    }
    
    /**
     * URL编码
     * @param string $str
     * @return string
     */
    protected function percentEncode($str)
    {
        $res = urlencode($str);
        $res = str_replace(['+', '*'], ['%20', '%2A'], $res);
        $res = preg_replace('/%7E/', '~', $res);
        return $res;
    }
}