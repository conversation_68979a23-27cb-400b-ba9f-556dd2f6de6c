<?php

namespace app\common\model;

use think\Model;

/**
 * 解禁规则模型
 */
class EsopVestingRule extends Model
{
    // 表名
    protected $name = 'esop_vesting_rules';

    // 自动时间戳
    protected $autoWriteTimestamp = false;

    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [
        'rule_type_text',
        'execution_type_text',
        'status_text'
    ];

    // 状态枚举
    public static $statusList = [
        1 => '启用',
        0 => '禁用'
    ];

    // 规则类型枚举
    public static $ruleTypeList = [
        1 => '按天数解禁',
        2 => '按比例解禁'
    ];

    // 执行类型枚举
    public static $executionTypeList = [
        1 => '每天',
        2 => '工作日',
        3 => '交易日'
    ];

    public function getStatusTextAttr($value, $data)
    {
        return self::$statusList[$data['status']] ?? '';
    }

    public function getRuleTypeTextAttr($value, $data)
    {
        return self::$ruleTypeList[$data['rule_type']] ?? '';
    }

    public function getExecutionTypeTextAttr($value, $data)
    {
        return self::$executionTypeList[$data['execution_type']] ?? '';
    }
} 