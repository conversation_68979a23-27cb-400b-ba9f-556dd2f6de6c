define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'template'], function ($, undefined, Backend, Table, Form, Template) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'uistyle/uistyle/index' + location.search,
                    add_url: 'uistyle/uistyle/add',
                    edit_url: 'uistyle/uistyle/edit',
                    del_url: 'uistyle/uistyle/del',
                    multi_url: 'uistyle/uistyle/multi',
                    table: 'esop_ui_styles',
                }
            });

            var table = $("#table");
            
            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'style_name', title: __('Style_name'), align: 'left'},
                        {field: 'main_color', title: __('Main_color'), formatter: function(value, row, index) {
                            return '<span class="label" style="background-color:' + value + ';">' + value + '</span>';
                        }},
                        {field: 'home_banner_url', title: __('Home_banner_url'), formatter: Table.api.formatter.image},
                        {field: 'inner_banner_url', title: __('Inner_banner_url'), formatter: Table.api.formatter.image},
                        {field: 'member_banner_url', title: __('Member_banner_url'), formatter: Table.api.formatter.image},
                        {field: 'is_default', title: __('Is_default'), searchList: {"0":__('Is_default 0'),"1":__('Is_default 1')}, formatter: Table.api.formatter.normal},
                        {field: 'created_at', title: __('Created_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'updated_at', title: __('Updated_at'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, 
                            buttons: [
                                {
                                    name: 'setdefault',
                                    text: __('Set default'),
                                    title: __('Set default'),
                                    classname: 'btn btn-xs btn-success btn-setdefault',
                                    hidden: function(row) {
                                        return row.is_default == 1;
                                    },
                                    extend: 'data-toggle="tooltip"',
                                    icon: 'fa fa-check',
                                    confirm: '确认要将此风格设为默认吗?',
                                    url: 'uistyle/uistyle/setdefault',
                                    success: function (data, ret) {
                                        $(".btn-refresh").trigger("click");
                                        return false;
                                    },
                                    error: function (data, ret) {
                                        Layer.alert(ret.msg);
                                        return false;
                                    }
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            
            // 使用浏览器原生的颜色选择器
            $('#c-main_color').attr('type', 'color');
        },
        edit: function () {
            Controller.api.bindevent();
            
            // 使用浏览器原生的颜色选择器
            $('#c-main_color').attr('type', 'color');
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 