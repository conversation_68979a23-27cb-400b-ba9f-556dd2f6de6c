<?php

namespace app\common\model;

use think\Model;

/**
 * 授权记录模型
 */
class EsopGrantOperationLog extends Model
{
    // 表名
    protected $name = 'esop_grant_operation_logs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = false;
    
    // 追加属性
    protected $append = [
        
    ];

    /**
     * 获取操作人信息
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }
    
    /**
     * 获取目标用户信息
     */
    public function targetUser()
    {
        return $this->belongsTo('EsopUser', 'target_user_id', 'id');
    }
    
    /**
     * 获取解禁规则信息
     */
    public function vestingRule()
    {
        return $this->belongsTo('EsopVestingRule', 'vesting_rule_id', 'id');
    }
} 