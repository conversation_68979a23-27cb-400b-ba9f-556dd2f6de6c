<?php

namespace app\common\model;

use think\Model;

/**
 * 行权记录模型
 */
class EsopExerciseRecords extends Model
{
    // 表名
    protected $name = 'esop_exercise_records';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'stock_price_type_text',
        'approval_status_text'
    ];
    
    // 定义价格类型获取器
    public function getStockPriceTypeTextAttr($value, $data)
    {
        $priceTypes = [
            1 => __('Average price'),
            2 => __('Agreement price')
        ];
        return isset($priceTypes[$data['stock_price_type']]) ? $priceTypes[$data['stock_price_type']] : '';
    }
    
    // 定义审批状态获取器
    public function getApprovalStatusTextAttr($value, $data)
    {
        $statusTypes = [
            0 => __('Pending approval'),
            1 => __('Approved'),
            2 => __('Rejected')
        ];
        return isset($statusTypes[$data['approval_status']]) ? $statusTypes[$data['approval_status']] : '';
    }
    
    // 关联用户
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id');
    }
    
    // 关联股票
    public function stock()
    {
        return $this->belongsTo('EsopStockManagement', 'stock_id', 'id');
    }
    
    // 关联审批人
    public function approvalUser()
    {
        return $this->belongsTo('Admin', 'approval_user_id', 'id');
    }
} 