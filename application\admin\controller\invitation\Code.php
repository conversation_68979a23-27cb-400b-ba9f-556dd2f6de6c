<?php

namespace app\admin\controller\invitation;

use app\common\controller\Backend;
use app\common\model\EsopInvitationCode;
use think\Db;
use think\Exception;

/**
 * 邀请码管理
 *
 * @icon fa fa-ticket
 */
class Code extends Backend
{
    /**
     * EsopInvitationCode模型对象
     * @var \app\common\model\EsopInvitationCode
     */
    protected $model = null;
    
    /**
     * 默认排序方式
     */
    protected $sort = 'id';
    protected $order = 'DESC';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\EsopInvitationCode;
        $this->view->assign("statusList", [0 => __('未使用'), 1 => __('已使用')]);
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 关联查询用户信息
            $list = Db::name('esop_invitation_codes')
                ->alias('ic')
                ->join('esop_users u', 'ic.user_id = u.id', 'LEFT')
                ->join('esop_user_profiles up', 'ic.user_id = up.user_id', 'LEFT')
                ->field([
                    'ic.*',
                    'up.real_name',
                    'u.phone'
                ])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            // 处理数据，添加会员字段
            $rows = [];
            foreach ($list->items() as $item) {
                $item = (array)$item;

                // 格式化会员字段：真实姓名(手机号)
                if ($item['user_id'] && ($item['real_name'] || $item['phone'])) {
                    $realName = $item['real_name'] ?: '未设置姓名';
                    $phone = $item['phone'] ?: '未绑定手机';
                    $item['member'] = $realName . '(' . $phone . ')';
                } else {
                    $item['member'] = $item['user_id'] ? '管理员生成' : '系统生成';
                }

                // 移除不需要的字段
                unset($item['real_name'], $item['phone']);

                $rows[] = $item;
            }

            $result = array("total" => $list->total(), "rows" => $rows);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 批量生成邀请码
     */
    public function generate()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if (!isset($params['count']) || intval($params['count']) <= 0) {
                    $this->error(__('请输入有效的生成数量'));
                }

                $count = intval($params['count']);
                $remark = isset($params['remark']) ? $params['remark'] : '';

                Db::startTrans();
                try {
                    $codes = EsopInvitationCode::generateCodes($count, $remark);
                    Db::commit();
                    return json(["code" => 1, "msg" => "成功生成" . count($codes) . "个邀请码", "data" => "", "url" => "", "wait" => 3]);
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
} 