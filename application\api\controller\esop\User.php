<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\EsopUser as EsopUserModel;
use app\common\model\EsopUserProfile as EsopUserProfileModel;
use app\common\model\EsopIdCardImage as EsopIdCardImageModel;
use app\common\library\SmsService;
use think\Validate;
use think\Cache;
use think\Db;
use fast\Random;
use app\common\library\Token;
use app\common\model\EsopBAccount as EsopBAccountModel;

/**
 * ESOP用户接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'sendsms', 'check', 'changepwd','initUserProfile','getUserProfile','initBankInfo'];
    protected $noNeedRight = '*';
    
    /**
     * 用户注册
     *
     * @param string $phone       手机号
     * @param string $password    密码
     * @param string $code        验证码
     * @param string $invitation_code 邀请码
     */
    public function register()
    {

        $phone = $this->request->post('phone');
        $password = $this->request->post('password');
        $code = $this->request->post('code');
        $invitation_code = $this->request->post('invitation_code');
        
        if (!$phone || !$password) {
            return $this->toError('参数错误');
        }
        
        // 验证手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\d{10}$/'
        ]);
        $data = [
            'phone' => $phone
        ];
        if (!$validate->check($data)) {
            return $this->toError('请输入正确的手机号');
        }
        
        // 检查验证码
        $captcha = Cache::get('sendsms_' . $phone);
        // if (!$captcha || $captcha != $code) {
        //     $this->error(__('Captcha is incorrect'));
        // }
        
        // 检查手机号是否已注册
        $user = EsopUserModel::getByPhone($phone);
        if ($user) {
            return $this->toError('手机号已存在');
        }
        
        // 验证邀请码
        if (!$invitation_code) {
            return $this->toError('请输入邀请码');
        }
        
        // 检查邀请码是否有效
        $invitationModel = new \app\common\model\EsopInvitationCode();
        $invitationCodeInfo = $invitationModel->where('code', $invitation_code)
                                         ->find();
        if (!$invitationCodeInfo) {
            return $this->toError('邀请码无效');
        }
        
        Db::startTrans();
        try {
            // 创建新用户
            $data = [
                'phone' => $phone,
                'password' => $password,
                'status' => 1
            ];
            
            $result = EsopUserModel::create($data);
            if ($result !== false) {
                $invitationCodeInfo->is_used = 1;
                $invitationCodeInfo->updated_at = date('Y-m-d H:i:s');
                $invitationCodeInfo->save();
                
                // 删除验证码
                Cache::rm('sendsms_' . $phone);
                
                $userInfo = EsopUserModel::getByPhone($phone);
                
                // 检查邀请码是否由其他用户生成的，如果是则建立邀请关系
                if ($invitationCodeInfo->user_id !== null && $invitationCodeInfo->user_id > 0) {
                    // 获取邀请人的B端账户ID(如果存在)
                    $bAccountId = null;
                    $bAccount = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$invitationCodeInfo->user_id}, user_ids)")
                        ->find();
                    if ($bAccount) {
                        $bAccountId = $bAccount->id;
                    }

                    //获取邀请人的邀请记录
                    $rootId = null;
                    $invitationRelation =\app\common\model\EsopInvitationRelation::where('invitee_id', $invitationCodeInfo->user_id)->limit(1)->find();
                    if ($invitationRelation) {
                        if($bAccountId){
                            $rootId = trim("$invitationRelation->root_id,{$invitationCodeInfo->user_id}",','); 
                        }else{
                            $rootId = $invitationRelation->root_id; 
                        }
                    }else{
                        $rootId = $invitationCodeInfo->user_id;
                    }

                    if(!$bAccount){
                        $rootIds = explode(',',$rootId);
                        if(!empty($rootIds[0])){
                            $bAccount = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$rootIds[0]}, user_ids)")->limit(1)->find();
                            if($bAccount){
                                $bAccountId = $bAccount->id;
                            }
                        }
                    }

                    // 创建邀请关系记录
                    \app\common\model\EsopInvitationRelation::createRelation(
                        $invitationCodeInfo->user_id,  // 邀请人ID
                        $userInfo->id,             // 被邀请人ID
                        $bAccountId,                // B端账户ID
                        $rootId,
                        $invitation_code,
                    );
                }
                
                // 初始化用户资产信息
                try {
                    \app\common\model\EsopUserAsset::changeUserAsset($userInfo->id, [
                        'available_assets' => 0,    // 可用资产
                        'pending_assets' => 0       // 冻结资产
                    ]);
                } catch (\Exception $assetEx) {
                    // 资产初始化失败，记录错误但不影响注册流程
                    \think\Log::write('用户资产初始化失败：' . $assetEx->getMessage(), 'error');
                }
                
                Db::commit();
                return $this->jsonSucess('注册成功，请完成实名认证', [
                    'user_info' => $userInfo->toArray(),
                    'need_certification' => true,
                    'certification_tip' => '请及时完成实名认证，认证通过后方可使用系统功能'
                ], 1);
            } else {
                Db::rollback();
                return $this->toError('注册失败');
            }
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError($e->getMessage());
        }
    }
    
    /**
     * 密码登录
     *
     * @param string $phone       手机号
     * @param string $password    密码
     */
    public function login()
    {
        $phone = $this->request->post('phone');
        $password = $this->request->post('password');
        
        if (!$phone || !$password) {
            return $this->toError('参数错误');
        }
        
        // 验证手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\d{10}$/'
        ]);
        $data = [
            'phone' => $phone
        ];
        if (!$validate->check($data)) {
            return $this->toError('请输入正确的手机号');
        }
        
        // 检查用户是否存在
        $user = EsopUserModel::getByPhone($phone);
        if (!$user) {
            return $this->toError('账户不存在');
        }
        
        // 检查账户状态
        if ($user['status'] != 1) {
            return $this->toError('账户已禁用');
        }
        
        // 检查用户实名认证状态
        $verificationStatus = \app\common\model\EsopUser::checkVerificationStatus($user['id']);
        if (!$verificationStatus['is_verified']) {
            return $this->toError($verificationStatus['status_text'],$verificationStatus);
        }
        
        // 检查密码
        if (!password_verify($password, $user['password'])) {
            return $this->toError('密码错误');
        }
        
        // 新增: 判断是否为B端用户
        // 若用户在 esop_b_accounts 表中存在记录, 则视为B端用户
        $isBUser = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$user->id}, user_ids)")
            ->value('id') ? 1 : 0;
        
        // 更新登录时间
        $user->last_login_time = time();
        $user->save();
        
        // 使用FastAdmin的Token系统
        $token = Random::uuid();
        $tokenExpire = 86400 * 7; // 7天有效期
        Token::set($token, $user->id, $tokenExpire);
        
        $data = [
            'id' => $user->id,
            'phone' => $user->phone,
            'token' => $token,
            'expires_in' => $tokenExpire,
            // 新增字段: 是否为B端用户
            'is_b_user' => $isBUser
        ];
        
        return $this->jsonSucess('登录成功', $data);
    }
    
    /**
     * 手机验证码登录
     *
     * @param string $phone    手机号
     * @param string $code     验证码
     */
    public function mobilelogin()
    {
        $phone = $this->request->post('phone');
        $code = $this->request->post('code');
        
        if (!$phone || !$code) {
            return $this->toError('参数错误');
        }
        
        // 验证手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\d{10}$/'
        ]);
        $data = [
            'phone' => $phone
        ];
        if (!$validate->check($data)) {
            return $this->toError('请输入正确的手机号');
        }
        
        // 检查验证码
        $captcha = Cache::get('sendsms_' . $phone);
        // if (!$captcha || $captcha != $code) {
        //     $this->error(__('Captcha is incorrect'));
        // }
        
        // 检查用户是否存在
        $user = EsopUserModel::getByPhone($phone);
        if (!$user) {
            return $this->toError('账户不存在，请先注册');
        }
        
        // 检查账户状态
        if ($user['status'] != 1) {
            return $this->toError('账户已禁用'); 
        }
        
        // 检查用户实名认证状态
        $verificationStatus = \app\common\model\EsopUser::checkVerificationStatus($user['id']);
        if (!$verificationStatus['is_verified']) {
            return $this->toError($verificationStatus['status_text']);
        }
        
        // 新增: 判断是否为B端用户
        // 若用户在 esop_b_accounts 表中存在记录, 则视为B端用户
        $isBUser = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$user->id}, user_ids)")
            ->value('id') ? 1 : 0;
        
        // 更新登录时间
        $user->last_login_time = time();
        $user->save();
        
        // 删除验证码
        Cache::rm('sendsms_' . $phone);
        
        // 使用FastAdmin的Token系统
        $token = Random::uuid();
        $tokenExpire = 86400 * 7; // 7天有效期
        Token::set($token, $user->id, $tokenExpire);
        
        $data = [
            'id' => $user->id,
            'phone' => $user->phone,
            'token' => $token,
            'expires_in' => $tokenExpire,
            // 新增字段: 是否为B端用户
            'is_b_user' => $isBUser
        ];
        
        return $this->jsonSucess('登录成功', $data);
    }
    
    /**
     * 发送短信验证码
     *
     * @param string $phone 手机号
     * @param string $event 事件名称 (login-登录, register-注册, changepwd-修改密码, changephone-修改手机号, setTradePassword-设置交易密码)
     */
    public function sendsms()
    {
        $phone = $this->request->post('phone');
        $event = $this->request->post('event');
        
        if (!$phone || !in_array($event, ['login', 'register', 'changepwd', 'changephone','setTradePassword'])) {
            return $this->toError('参数错误');   
        }
        
        // 验证手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\d{10}$/'
        ]);
        $data = [
            'phone' => $phone
        ];
        if (!$validate->check($data)) {
            return $this->toError('请输入正确的手机号');
        }
        
        // 如果是注册，检查手机号是否已注册
        if ($event == 'register') {
            $user = EsopUserModel::getByPhone($phone);
            if ($user) {
                return $this->toError('手机号已存在');
            }
        }
        
        // 如果是修改手机号，检查新手机号是否被占用
        if ($event == 'changephone') {
            $user = EsopUserModel::getByPhone($phone);
            if ($user) {
                return $this->toError('手机号已存在');
            }
        }
        
        // 检查是否频繁发送
        $last = Cache::get('last_sendsms_' . $phone);
        // if ($last && time() - $last < 60) {
        //     return $this->toError('发送过于频繁，请稍后再试');
        // }
        
        // 生成验证码
        $code = Random::numeric(6);
        
        // 发送验证码
        $smsService = new SmsService();
        $result = $smsService->send($phone, $code, $event);
        if (!$result) {
            // 获取发送失败的具体原因
            $error = $smsService->getError();
            // 如果是开发环境，或者SDK未安装，直接返回验证码用于测试
            if (config('app_debug') || strpos($error, 'SDK未安装') !== false) {
                // 保存验证码和发送时间
                Cache::set('sendsms_' . $phone, $code, 600); // 验证码10分钟有效
                Cache::set('last_sendsms_' . $phone, time(), 600);
                
                return $this->jsonSucess('验证码发送成功' . ' (调试模式)', ['code' => $code]);
            } else {
                return $this->toError($error ?: '短信发送失败');
            }
        }
        
        // 保存验证码和发送时间
        Cache::set('sendsms_' . $phone, $code, 600); // 验证码10分钟有效
        Cache::set('last_sendsms_' . $phone, time(), 600);
        
        return $this->jsonSucess('验证码发送成功');
    }
    
    /**
     * 检查手机号是否存在
     *
     * @param string $phone 手机号
     */
    public function check()
    {
        $phone = $this->request->post('phone');
        
        if (!$phone) {
            return $this->toError('参数错误');
        }
        
        // 验证手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\d{10}$/'
        ]);
        $data = [
            'phone' => $phone
        ];
        if (!$validate->check($data)) {
            return $this->toError('请输入正确的手机号');
        }
        
        $user = EsopUserModel::getByPhone($phone);
        return $this->jsonSucess('', ['exists' => (bool)$user]);
    }
    
    /**
     * 通过验证码重置密码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $new_password 新密码
     */
    public function changepwd()
    {
        $phone = $this->request->post('phone');
        $code = $this->request->post('code');
        $new_password = $this->request->post('new_password');
        
        if (!$phone || !$code || !$new_password) {
            return $this->toError('参数错误');
        }
        
        // 验证手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\d{10}$/'
        ]);
        $data = [
            'phone' => $phone
        ];
        if (!$validate->check($data)) {
            return $this->toError('请输入正确的手机号');
        }
        
        // 检查验证码
        $captcha = Cache::get('sendsms_' . $phone);
        // if (!$captcha || $captcha != $code) {
        //     $this->error(__('Captcha is incorrect'));
        // }
        
        // 检查用户是否存在
        $user = EsopUserModel::getByPhone($phone);
        if (!$user) {
            return $this->toError('账户不存在');
        }
        
        // 更新密码
        $user->password = $new_password;
        if ($user->save()) {
            // 删除验证码
            Cache::rm('sendsms_' . $phone);
            
            return $this->jsonSucess('密码重置成功');
        } else {
            return $this->toError('密码重置失败');
        }
    }
    
    /**
     * 注销登录
     */
    public function logout()
    {
        // 使用FastAdmin标准注销
        $this->auth->logout();
        return $this->jsonSucess('注销成功');
    }
    
    /**
     * 获取其他用户资料
     * 
     * @param int $user_id 用户ID
     */
    public function userInfo()
    {
        // FastAdmin已自动验证登录状态
        $userId = $this->request->post('user_id');
        
        if (!$userId) {
            return $this->toError('参数错误');
        }
        
        $user = EsopUserModel::get($userId);
        if (!$user) {
            return $this->toError('用户不存在');
        }
        
        // 只返回基本资料，保护隐私
        $data = [
            'id' => $user->id,
            'nickname' => $user->nickname,
            'avatar' => $user->avatar,
            'gender' => $user->gender,
            'gender_text' => $user->gender_text
        ];
        
        return $this->jsonSucess('', $data);
    }
    
    /**
     * 获取用户详细资料（通过手机号）
     * @param string $phone 手机号（GET参数）
     * @return \think\Response
     */
    public function getUserProfile()
    {
        // 获取GET参数中的手机号
        $phone = $this->request->get('phone');
        if (!$phone) {
            // 未传递手机号参数，返回错误
            return $this->toError('请提供手机号');
        }
        // 校验手机号格式
        if (!preg_match('/^1\\d{10}$/', $phone)) {
            return $this->toError('手机号格式不正确');
        }
        // 通过手机号查找用户
        $user = \app\common\model\EsopUser::getByPhone($phone);
        if (!$user) {
            return $this->toError('用户不存在');
        }
        $userId = $user->id;
        // 通过用户ID获取用户资料
        $profile = \app\common\model\EsopUserProfile::getByUserId($userId);
        if (!$profile) {
            return $this->jsonSucess('用户资料不存在', [
                'has_profile' => false
            ]);
        }
        // 返回用户资料数据
        $data = $profile->toArray();
        $data['has_profile'] = true;
        // 判断是否设置了交易密码，用布尔值替代直接返回密码
        if (isset($data['trade_password']) && !empty($data['trade_password'])) {
            $data['has_trade_password'] = true;
            // 移除交易密码字段，避免泄露
            unset($data['trade_password']);
        } else {
            $data['has_trade_password'] = false;
        }

        // 获取身份证图片信息
        $idCardImages = \app\common\model\EsopIdCardImage::getByUserId($userId);
        if ($idCardImages) {
            // 有图片，返回图片信息
            $data['id_card'] = [
                'front_image' => $idCardImages->front_image,
                'back_image' => $idCardImages->back_image,
                'is_verified' => $idCardImages->is_verified,
                'verification_status_text' => $idCardImages->verification_status_text
            ];
        } else {
            // 无图片，返回空和未验证
            $data['id_card'] = [
                'front_image' => '',
                'back_image' => '',
                'is_verified' => 0,
                'verification_status_text' => '未验证'
            ];
        }

        // 获取用户自己的邀请码
        $invitationCodeRow = \app\common\model\EsopInvitationCode::where('user_id', $userId)->limit(1)->find();
        if ($invitationCodeRow && !empty($invitationCodeRow->code)) {
            $data['invitation_code'] = $invitationCodeRow->code;
        } else {
            $data['invitation_code'] = '';
        }

        return $this->jsonSucess('', $data);
    }
    
    /**
     * 获取身份证图片
     */
    public function getIdCardImages()
    {
        // 使用FastAdmin标准认证，获取当前用户身份证图片
        $userId = $this->auth->id;
        $images = EsopIdCardImageModel::getByUserId($userId);
        
        if (!$images) {
            return $this->jsonSucess('身份证图片不存在', [
                'has_images' => false
            ]);
        }
        
        // 返回身份证图片数据
        $data = $images->toArray();
        $data['has_images'] = true;
        
        return $this->jsonSucess('', $data);
    }
    
    /**
     * 上传身份证图片
     * 
     * @param string $front_image 身份证正面图片
     * @param string $back_image 身份证反面图片
     */
    public function uploadIdCardImages()
    {
        // 获取请求参数
        $frontImage = $this->request->post('front_image');
        $backImage = $this->request->post('back_image');
        
        if (!$frontImage || !$backImage) {
            return $this->toError('身份证正面和背面图片不能为空');
        }
        
        // 验证图片格式
        $urlPattern = '/^(\/uploads\/|http:\/\/|https:\/\/)/i';
        if (!preg_match($urlPattern, $frontImage) || !preg_match($urlPattern, $backImage)) {
            return $this->toError('图片格式不正确');
        }
        
        // 创建或更新身份证图片
        try {
            $userId = $this->auth->id;
            $images = EsopIdCardImageModel::createOrUpdate($userId, $frontImage, $backImage);
            
            return $this->jsonSucess('身份证图片上传成功', $images);
        } catch (\Exception $e) {
            return $this->toError('身份证图片上传失败' . ': ' . $e->getMessage());
        }
    }
    
    /**
     * 更新用户详细资料（包含身份证信息）
     *
     * @param string $real_name 真实姓名
     * @param int $gender 性别：1-男，2-女
     * @param date $birth_date 出生日期
     * @param string $id_card_no 身份证号码
     * @param int $marital_status 婚姻状态：1-未婚，2-已婚，3-离异，4-丧偶
     * @param string $securities_account 证券账户
     * @param string $front_image 身份证正面图片
     * @param string $back_image 身份证反面图片
     * @param string $phone 手机号
     */
    public function initUserProfile()
    {
        // 获取请求参数
        $data = $this->request->post();
        
        // 验证基本信息数据
        $rule = [
            'real_name' => 'require|length:2,50',
            'gender' => 'require|in:0,1,2',
            'birth_date' => 'require|date',
            'id_card_no' => 'require',
            'marital_status' => 'require|in:0,1,2,3,4',
            'securities_account' => 'length:0,50',
            'front_image' => 'require',
            'back_image' => 'require',
            'phone' => 'require|regex:/^1\d{10}$/',
            // 添加银行卡相关验证规则（可选字段）
            'bank_name' => 'length:0,50',
            'bank_branch' => 'length:0,50',
            'bank_account' => 'length:0,50',
            'bank_image' => 'length:0,255'
        ];
        
        $msg = [
            'real_name.require' => '请输入真实姓名',
            'real_name.length' => '真实姓名长度必须在2-50个字符之间',
            'gender.require' => '请选择性别',
            'gender.in' => '性别选项无效',
            'birth_date.require' => '请选择出生日期',
            'birth_date.date' => '出生日期格式不正确',
            'id_card_no.require' => '请输入身份证号码',
            'id_card_no.regex' => '身份证号码格式不正确',
            'marital_status.require' => '请选择婚姻状态',
            'marital_status.in' => '婚姻状态选项无效',
            'securities_account.length' => '证券账户长度不能超过50个字符',
            'front_image.require' => '请上传身份证正面图片',
            'back_image.require' => '请上传身份证反面图片',
            'phone.require' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
            // 添加银行卡相关验证消息
            'bank_name.length' => '开户行名称长度不能超过50个字符',
            'bank_branch.length' => '开户支行长度不能超过50个字符',
            'bank_account.length' => '银行卡号长度不能超过50个字符',
            'bank_image.length' => '银行卡图片路径长度不能超过255个字符'
        ];
        
        $validate = new Validate($rule, $msg);
        if (!$validate->check($data)) {
            return $this->toError($validate->getError());
        }

        // 身份证号码正则表达式验证
        $idCardPattern = '/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/';
        if (!preg_match($idCardPattern, $data['id_card_no'])) {
            return $this->toError('身份证号码格式不正确');
        }
        
        // 验证身份证号码校验码
        if (!$this->validateIdCardChecksum($data['id_card_no'])) {
            return $this->toError('身份证号码校验码错误');
        }
       
        // 身份证号与出生日期一致性校验
        if (isset($data['id_card_no']) && isset($data['birth_date'])) {
            $birthFromIdCard = substr($data['id_card_no'], 6, 4) . '-' . substr($data['id_card_no'], 10, 2) . '-' . substr($data['id_card_no'], 12, 2);
            if ($birthFromIdCard != $data['birth_date']) {
                return $this->toError('出生日期与身份证号码不一致');
            }
        }
        
        // 检查是否上传了身份证图片
        $frontImage = isset($data['front_image']) ? $data['front_image'] : null;
        $backImage = isset($data['back_image']) ? $data['back_image'] : null;
        
        // 通过手机号查找用户
        $phone = $data['phone'];
        $user = \app\common\model\EsopUser::getByPhone($phone);
        if (!$user) {
            return $this->toError('用户不存在，请先注册');
        }
        $userId = $user->id;
        
        Db::startTrans();
        try {
            // 1. 创建或更新用户基本资料
            $profileData = array_intersect_key($data, array_flip(['real_name', 'gender', 'birth_date', 'id_card_no', 'marital_status', 'securities_account', 'bank_name', 'bank_branch', 'bank_account', 'bank_image']));
            $profileData['audit_status'] = 0;
            $profile = EsopUserProfileModel::createOrUpdate($userId, $profileData);

            // 2. 如果提供了身份证图片，则更新图片
            if ($frontImage && $backImage) {
                // 验证图片格式
                $urlPattern = '/^(\/uploads\/|http:\/\/|https:\/\/)/i';
                if (!preg_match($urlPattern, $frontImage) || !preg_match($urlPattern, $backImage)) {
                    throw new \Exception('图片格式不正确');
                }
                
                $images = EsopIdCardImageModel::createOrUpdate($userId, $frontImage, $backImage);
            }
            
            Db::commit();
            
            // 获取完整的用户资料和身份证图片
            $result = [
                'profile' => $profile->toArray()
            ];
            
            // 加入身份证图片信息（如果有）
            $idCardImages = EsopIdCardImageModel::getByUserId($userId);
            if ($idCardImages) {
                $result['id_card_images'] = $idCardImages->toArray();
            }
            
            return $this->jsonSucess('用户资料更新成功', $result);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError('用户资料更新失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 更新银行卡信息
     *
     * @param string $bank_name 开户行名称（可选）
     * @param string $bank_branch 开户支行（可选）
     * @param string $bank_account 银行卡号（可选）
     * @param string $bank_image 银行卡图片（可选）
     * @param string $phone 手机号（必填，用于验证用户身份）
     */
    public function initBankInfo()
    {
        // 获取请求参数
        $data = $this->request->post();
        
        // 如果没有提交任何数据，返回错误
        if (empty($data)) {
            return $this->toError('未提交任何数据');
        }
        
        // 验证手机号（必填）
        if (!isset($data['phone']) || empty($data['phone'])) {
            return $this->toError('手机号不能为空');
        }
        
        // 验证基本信息数据，所有银行卡字段都是可选的
        $rule = [
            'phone' => 'require|regex:/^1\d{10}$/',
            'bank_name' => 'length:0,50',
            'bank_branch' => 'length:0,50',
            'bank_account' => 'length:0,50',
            'bank_image' => 'length:0,255'
        ];
        
        $msg = [
            'phone.require' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
            'bank_name.length' => '开户行名称长度不能超过50个字符',
            'bank_branch.length' => '开户支行长度不能超过50个字符',
            'bank_account.length' => '银行卡号长度不能超过50个字符',
            'bank_image.length' => '银行卡图片路径长度不能超过255个字符'
        ];
        
        $validate = new Validate($rule, $msg);
        if (!$validate->check($data)) {
            return $this->toError($validate->getError());
        }
        
        // 通过手机号查找用户
        $phone = $data['phone'];
        $user = \app\common\model\EsopUser::getByPhone($phone);
        if (!$user) {
            return $this->toError('用户不存在，请先注册');
        }
        $userId = $user->id;
        
        // 过滤出银行卡相关字段
        $bankData = array_intersect_key($data, array_flip(['bank_name', 'bank_branch', 'bank_account', 'bank_image']));
        
        // 如果银行卡号不为空，进行格式验证
        if (!empty($bankData['bank_account'])) {
            // 银行卡号格式验证（16-19位数字）
            if (!preg_match('/^\d{16,19}$/', $bankData['bank_account'])) {
                return $this->toError('银行卡号格式不正确，请输入16-19位数字');
            }
        }
        
        // 如果银行卡图片不为空，验证图片格式
        if (!empty($bankData['bank_image'])) {
            $urlPattern = '/^(\/uploads\/|http:\/\/|https:\/\/)/i';
            if (!preg_match($urlPattern, $bankData['bank_image'])) {
                return $this->toError('银行卡图片格式不正确');
            }
        }
        
        Db::startTrans();
        try {
            // 获取用户资料
            $profile = EsopUserProfileModel::getByUserId($userId);
            if (!$profile) {
                // 如果用户资料不存在，创建一个新的资料
                $profileData = array_merge($bankData, [
                    'user_id' => $userId,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                $profile = EsopUserProfileModel::create($profileData);
            } else {
                // 如果用户资料已存在，只更新银行卡相关字段
                foreach ($bankData as $key => $value) {
                    if ($value !== null && $value !== '') {
                        $profile->$key = $value;
                    }
                }
                $profile->updated_at = date('Y-m-d H:i:s');
                $profile->save();
            }
            
            Db::commit();
            
            // 返回更新后的银行卡信息
            $result = [
                'bank_name' => $profile->bank_name,
                'bank_branch' => $profile->bank_branch,
                'bank_account' => $profile->bank_account,
                'bank_image' => $profile->bank_image,
                'updated_at' => $profile->updated_at
            ];
            
            return $this->jsonSucess('银行卡信息更新成功', $result);
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError('银行卡信息更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 验证身份证号码校验码
     * 
     * @param string $idCard 身份证号码
     * @return bool 校验结果
     */
    protected function validateIdCardChecksum($idCard)
    {
        // 17位系数
        $factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        // 校验码对应值
        $verifyNumbers = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        
        // 取出前17位
        $idCardBase = substr($idCard, 0, 17);
        
        // 取出校验码
        $verifyCode = strtoupper(substr($idCard, 17, 1));
        
        // 根据前17位计算校验码
        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += intval($idCardBase[$i]) * $factor[$i];
        }
        
        $mod = $sum % 11;
        $calcVerifyCode = $verifyNumbers[$mod];
        
        return $verifyCode === $calcVerifyCode;
    }
    
    /**
     * 获取用户实名认证状态
     * @ApiMethod (GET)
     * @ApiReturn  {"code":1,"msg":"获取成功","data":{"has_profile":true,"audit_status":0,"audit_status_text":"待审核"}}
     */
    public function realNameStatus()
    {
        // 使用FastAdmin标准认证，获取当前用户ID
        $userId = $this->auth->id;
        
        // 查询用户资料
        $profile = EsopUserProfileModel::getByUserId($userId);
        
        if (!$profile) {
            // 用户未提交资料
            return $this->jsonSucess('用户未提交实名认证', [
                'has_profile' => false,
                'audit_status' => null,
                'audit_status_text' => '未提交'
            ]);
        }
        
        // 获取审核状态文本
        $auditStatusMap = [
            0 => '待审核',
            1 => '审核通过',
            2 => '审核不通过'
        ];
        
        // 返回实名认证状态
        return $this->jsonSucess('获取成功', [
            'has_profile' => true,
            'audit_status' => $profile->audit_status,
            'audit_status_text' => isset($auditStatusMap[$profile->audit_status]) 
                ? $auditStatusMap[$profile->audit_status] 
                : '未知状态'
        ]);
    }
    
    /**
     * 更新用户详细资料（所有字段可选）
     *
     * @param string $avatar 头像（可选）
     * @param string $nickname 昵称（可选）
     * @param int $gender 性别：1-男，2-女（可选）
     * @param date $birth_date 出生日期（可选）
     */
    public function updateUserProfile()
    {
        // 获取请求参数
        $data = $this->request->post();
        
        // 如果没有提交任何数据，返回错误
        if (empty($data)) {
            return $this->toError('未提交任何数据');
        }
        
        // 对特殊字段进行处理
        // 确保gender是整数
        if (isset($data['gender']) && !is_int($data['gender'])) {
            $data['gender'] = intval($data['gender']);
        }
        
        // 验证基本信息数据，所有字段都是可选的
        $rule = [
            'nickname' => 'length:2,50',
            'gender' => 'in:0,1,2',
            'birth_date' => 'date'
        ];
        
        $msg = [
            'nickname.length' => '昵称长度必须在2-50个字符之间',
            'gender.in' => '性别选项无效',
            'birth_date.date' => '出生日期格式不正确'
        ];
        
        $validate = new Validate($rule, $msg);
        if (!$validate->check($data)) {
            return $this->toError($validate->getError());
        }
        
        // 对avatar单独验证
        if (isset($data['avatar']) && !empty($data['avatar'])) {
            if (!is_string($data['avatar'])) {
                return $this->toError('头像必须是字符串');
            }
        }

        Db::startTrans();
        try {
            $userId = $this->auth->id;
            
            // 只更新用户提交的字段
            $profileData = array_intersect_key($data, array_flip(['nickname', 'avatar', 'gender', 'birth_date']));
            
            // 检查是否存在用户资料
            $profile = EsopUserProfileModel::getByUserId($userId);
            if (!$profile) {
                // 如果用户资料不存在，需要创建一个新的资料
                $profile = EsopUserProfileModel::createOrUpdate($userId, $profileData);
            } else {
                // 如果用户资料已存在，只更新提交的字段
                foreach ($profileData as $key => $value) {
                    $profile->$key = $value;
                }
                $profile->save();
            }
            
            Db::commit();
            
            // 返回更新后的用户资料
            return $this->jsonSucess('用户资料更新成功', $profile->toArray());
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError('用户资料更新失败: ' . $e->getMessage());
        }
    }

    /**
     * 修改手机号
     *
     * @param string $phone 新手机号
     * @param string $code  验证码
     */
    public function changePhone()
    {
        // 获取参数
        $newPhone = $this->request->post('phone');
        $code = $this->request->post('code');
        
        // 参数校验
        if (!$newPhone || !$code) {
            return $this->toError('参数错误');
        }
        
        // 校验手机号格式
        $validate = new Validate([
            'phone' => 'require|regex:/^1\\d{10}$/'
        ]);
        if (!$validate->check(['phone' => $newPhone])) {
            return $this->toError('请输入正确的手机号');
        }
        
        // 校验验证码
        $captcha = Cache::get('sendsms_' . $newPhone);
        // if (!$captcha || $captcha != $code) {
        //     return $this->toError('验证码错误');
        // }
        
        // 检查新手机号是否已存在
        $existUser = EsopUserModel::getByPhone($newPhone);
        if ($existUser) {
            return $this->toError('手机号已存在');
        }
        
        // 获取当前登录用户
        $user = EsopUserModel::get($this->auth->id);
        if (!$user) {
            return $this->toError('用户不存在');
        }
        
        // 更新手机号
        $user->phone = $newPhone;
        if ($user->save()) {
            // 清除验证码缓存
            Cache::rm('sendsms_' . $newPhone);
            
            return $this->jsonSucess('手机号修改成功', [
                'phone' => $newPhone
            ]);
        } else {
            return $this->toError('手机号修改失败');
        }
    }

    /**
     * 获取B端用户信息
     *
     * @ApiMethod (GET)
     * @ApiSummary  获取当前登录用户所属的 B 端账户信息
     * @ApiReturn   {"code":1,"msg":"获取成功","data":{"id":1,"account_name":"示例B端","ui_style_id":2,"ui_style_text":"科技蓝","available_assets":10000,"invested_assets":5000,"exercise_fee_rate":0.05,"team_member_count":12,"vesting_rules":[{"id":1,"rule_name":"按天数解禁","rule_type":1}]}}
     */
    public function bUserInfo()
    {
        // 1. 获取当前登录用户ID（已通过 FastAdmin 的 Auth 登录检测）
        $userId = $this->auth->id;

        // 2. 查询用户是否为 B 端管理员（即在 esop_b_accounts 表存在记录）
        $bAccount = EsopBAccountModel::whereRaw("FIND_IN_SET({$userId}, user_ids)")
            ->find();

        // 如果当前用户不是 B 端管理员，再尝试检查是否为 B 端团队成员（邀请关系）
        if (!$bAccount) {
            $invitationRelation = \app\common\model\EsopInvitationRelation::where('invitee_id', $userId)->find();
            if ($invitationRelation->b_account_id) {
                $bAccount = EsopBAccountModel::where('id', $invitationRelation->b_account_id)
                    ->find();
            }else{
                $rootIds = explode(',',$invitationRelation->root_id);
                if(!empty($rootIds[0])){
                    $bAccount = \app\common\model\EsopBAccount::whereRaw("FIND_IN_SET({$rootIds[0]}, user_ids)")->limit(1)->find();
                }
            }
        }

        // 若仍未找到 B 端信息，则返回错误
        if (!$bAccount) {
            return $this->toError('非B端用户，无法获取B端信息');
        }

        // 3. 组装返回数据
        $data                      = $bAccount->toArray();
        $data['ui_style_text']     = $bAccount->ui_style_text;          // UI 风格中文文本
        $data['team_member_count'] = $bAccount->getTeamMemberCount();   // 团队成员数量
        // 获取UI风格详细信息
        $uiStyleId = $bAccount->ui_style_id;
        $uiStyleInfo = [];
        if ($uiStyleId) {
            // 查询esop_ui_styles表，获取风格详细信息
            $uiStyleInfo = \think\Db::name('esop_ui_styles')
                ->where('id', $uiStyleId)
                ->field('id, style_name, main_color, home_banner_url, inner_banner_url, member_banner_url, is_default')
                ->find();
            if ($uiStyleInfo) {
                $data['ui_style'] = [
                    'id'                => $uiStyleInfo['id'],
                    'style_name'        => $uiStyleInfo['style_name'],
                    'main_color'        => $uiStyleInfo['main_color'],
                    'home_banner_url'   => $uiStyleInfo['home_banner_url'],
                    'inner_banner_url'  => $uiStyleInfo['inner_banner_url'],
                    'member_banner_url' => $uiStyleInfo['member_banner_url'],
                ];
            } else {
                $data['ui_style'] = null;
            }
        } else {
            $data['ui_style'] = null;
        }

        // 4. 获取解禁规则信息
        $vestingRules = $bAccount->getVestingRules();
        $data['vesting_rules'] = !empty($vestingRules) ? collection($vestingRules)->toArray() : [];

        return $this->jsonSucess('获取成功', $data);
    }

    /**
     * 设置B端平台信息
     *
     * @ApiMethod (POST)
     * @ApiSummary  更新当前登录 B 端管理员所属平台信息
     * @ApiParams   (name="account_name", type="string", required=false, description="B端名称")
     * @ApiParams   (name="ui_style_id", type="integer", required=false, description="UI风格ID 1-中国红 2-科技蓝 3-极致黑 4-青春绿")
     * @ApiParams   (name="exercise_fee_rate", type="float", required=false, description="行权手续费率（%），0-100 之间")
     * @ApiReturn   {"code":1,"msg":"更新成功","data":{...}}
     */
    public function setBUserInfo()
    {
        // 1. 获取当前登录用户ID
        $userId = $this->auth->id;

        // 2. 判断是否为 B 端管理员
        $bAccount = EsopBAccountModel::whereRaw("FIND_IN_SET({$userId}, user_ids)")
            ->find();
        if (!$bAccount) {
            return $this->toError('非B端管理员，无法设置平台信息');
        }

        // 3. 接收参数（全部可选）
        $input = [
            'account_name'      => $this->request->post('account_name', null),
            'ui_style_id'       => $this->request->post('ui_style_id', null),
            'exercise_fee_rate' => $this->request->post('exercise_fee_rate', null),
        ];

        // 过滤掉未提供的字段
        $input = array_filter($input, function ($v) {
            return $v !== null && $v !== '';
        });

        // 若一个字段都未提供
        if (empty($input)) {
            return $this->toError('未提交任何数据');
        }

        // 4. 动态构造验证规则
        $rules = [];
        $messages = [];
        if (isset($input['account_name'])) {
            $rules['account_name'] = 'length:1,100';
            $messages['account_name.length'] = 'B端名称长度需在1-100个字符之间';
        }
        if (isset($input['ui_style_id'])) {
            $rules['ui_style_id'] = 'in:1,2,3,4';
            $messages['ui_style_id.in'] = 'UI风格ID无效';
        }
        if (isset($input['exercise_fee_rate'])) {
            $rules['exercise_fee_rate'] = 'float|between:0,100';
            $messages['exercise_fee_rate.float'] = '行权手续费率必须为数字';
            $messages['exercise_fee_rate.between'] = '行权手续费率必须在0-100之间';
        }

        $validate = new Validate($rules, $messages);
        if (!$validate->check($input)) {
            return $this->toError($validate->getError());
        }

        // 5. 更新数据：仅更新提交的字段
        foreach ($input as $field => $value) {
            $bAccount->$field = $value;
        }

        if ($bAccount->save()) {
            // 返回更新后的B端信息
            $data                      = $bAccount->toArray();
            $data['ui_style_text']     = $bAccount->ui_style_text;
            $data['team_member_count'] = $bAccount->getTeamMemberCount();
            return $this->jsonSucess('更新成功', $data);
        } else {
            return $this->toError('更新失败，请稍后重试');
        }
    }

    /**
     * 生成邀请二维码
     *
     * B 端管理员为指定手机号生成邀请码，并返回二维码图片地址。
     *
     * @ApiMethod (POST)
     * @ApiSummary  生成邀请二维码
     * @ApiParams   (name="phone", type="string", required=true, description="被邀请人手机号")
     * @ApiReturn   {"code":1,"msg":"生成成功","data":{"invitation_code":"ABCDEFGH","qrcode_url":"http://domain.com/uploads/qrcodes/********/xxx.png"}}
     */
    public function generateInvitationQrcode()
    {
        // 1. 获取当前登录用户ID（邀请人）
        $userId = $this->auth->id;

        $registerUrl = $this->request->post('register_url');

        if (!$registerUrl) {
            return $this->toError('注册页面URL不能为空');
        }
        
        // 2. 生成邀请码并生成二维码
        try {
            Db::startTrans();

            // 2.1 生成邀请码并持久化
            $codes = \app\common\model\EsopInvitationCode::generateCodes(1, '团队邀请', $userId);
            $invitationCode = $codes[0] ?? null;
            if (!$invitationCode) {
                throw new \Exception('邀请码生成失败');
            }

            // 2.2 构造二维码内容：注册页面URL + 邀请人ID + 邀请码
            // 形如: http://domain.com/index.php/register?inviter_id=123&invitation_code=ABCDEFG
            $qrCodeContent = sprintf(
                "%s?inviter_id=%d&invitation_code=%s",
                $registerUrl,
                $userId,
                $invitationCode
            );

            // 2.3 生成二维码图片
            $domain = $this->request->domain();
            $qrRelativePath = \app\common\library\QrcodeService::generate($qrCodeContent);
            $qrcodeUrl = $domain . $qrRelativePath;

            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError('邀请二维码生成失败: ' . $e->getMessage());
        }

        // 3. 返回
        return $this->jsonSucess('邀请二维码生成成功', [
            'invitation_code' => $invitationCode,
            'qrcode_url'      => $qrcodeUrl,
            'inviter_id'      => $userId,
        ]);
    }

    /**
     * 设置交易密码
     *
     * @param string $code 验证码
     * @param string $password 交易密码
     * @param string $password_confirm 确认密码
     * @ApiMethod (POST)
     * @ApiReturn {"code":1,"msg":"交易密码设置成功","data":[]}
     */
    public function setTradePassword()
    {
        // 获取参数
        $code = $this->request->post('code');
        $password = $this->request->post('password');
        $passwordConfirm = $this->request->post('password_confirm');
        
        // 参数验证
        if (!$code || !$password || !$passwordConfirm) {
            return $this->toError('参数错误');
        }
        
        // 获取当前登录用户
        $userId = $this->auth->id;
        $user = \app\common\model\EsopUser::get($userId);
        if (!$user) {
            return $this->toError('用户不存在');
        }
        
        $phone = $user->phone;
        
        // 检查交易密码长度和复杂性
        if (strlen($password) < 6) {
            return $this->toError('交易密码长度不能少于6位');
        }
        
        // 检查两次密码是否一致
        if ($password !== $passwordConfirm) {
            return $this->toError('两次输入的密码不一致');
        }
        
        // 验证手机验证码
        $captcha = Cache::get('sendsms_' . $phone);
        // if (!$captcha || $captcha != $code) {
        //     return $this->toError('验证码错误');
        // }
        
        // 设置交易密码
        try {
            $result = \app\common\model\EsopUserProfile::setTradePassword($userId, $password);
            
            if ($result) {
                // 清除验证码缓存
                Cache::rm('sendsms_' . $phone);
                
                return $this->jsonSucess('交易密码设置成功');
            } else {
                return $this->toError('交易密码设置失败');
            }
        } catch (\Exception $e) {
            return $this->toError('交易密码设置失败: ' . $e->getMessage());
        }
    }

    /**
     * 注销账号
     *
     * @param string $code 验证码
     * @param int $is_read_risk_warning 是否已阅读风险提示 (1-是，0-否)
     * @ApiMethod (POST)
     * @ApiReturn {"code":1,"msg":"账号注销成功","data":[]}
     */
    public function deleteAccount()
    {
        // 获取参数
        $code = $this->request->post('code');
        $isReadRiskWarning = $this->request->post('is_read_risk_warning', 0);
        
        // 参数验证
        if (!$code) {
            return $this->toError('验证码不能为空');
        }
        
        if (!$isReadRiskWarning) {
            return $this->toError('请阅读并确认注销账号的风险提示');
        }
        
        // 获取当前登录用户
        $userId = $this->auth->id;
        $user = \app\common\model\EsopUser::get($userId);
        if (!$user) {
            return $this->toError('用户不存在');
        }
        
        $phone = $user->phone;
        
        // 验证手机验证码
        $captcha = Cache::get('sendsms_' . $phone);
        // if (!$captcha || $captcha != $code) {
        //     return $this->toError('验证码错误');
        // }
        
        // 开始事务
        Db::startTrans();
        try {
            // 1. 处理用户资料
            $ok = \app\common\model\EsopUserProfile::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户资料删除失败');
            }
            
            // 2. 处理用户的邀请关系
            $ok = \app\common\model\EsopInvitationRelation::where('inviter_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('邀请关系删除失败');
            }

            // 3. 处理用户的邀请码
            $ok = \app\common\model\EsopInvitationCode::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('邀请码删除失败');
            }

            //4. 删除用户详情
            $ok = \app\common\model\EsopUserProfile::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户详情删除失败');
            }
            
            //5. 删除用户身份证图片
            $ok = \app\common\model\EsopIdCardImages::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户身份证图片删除失败');
            }
            
            //6. 删除用户意见反馈
            $ok = \app\common\model\Feedback::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户意见反馈删除失败');
            }

            //7. 删除授权信息
            $ok = \app\common\model\EsopGrantOperationLog::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('授权信息删除失败');
            }

            //8. 删除用户文章
            $ok = \app\common\model\EsopArticle::where('author_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户文章删除失败');
            }

            //9. 删除用户已读公告
            $ok = \app\common\model\EsopNoticeReadRecord::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户已读公告删除失败');
            }

            //10. 删除用户公告审批记录
            $ok = \app\common\model\EsopNoticeApprovalRecord::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户公告审批记录删除失败');
            }

            //11. 删除用户股票
            $ok = \app\common\model\EsopUserStockRecords::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户股票删除失败');
            }

            //12. 删除用户资产
            $ok = \app\common\model\EsopUserAsset::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户资产删除失败');
            }

            //13. 删除用户资金变化记录
            $ok = \app\common\model\EsopUserFundChangeRecords::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户资金变化记录删除失败');
            }

            //14. 删除用户转出记录
            $ok = \app\common\model\EsopTransferRecord::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户转出记录删除失败');
            }

            //15. 删除用户换股记录
            $ok = \app\common\model\EsopExchangeRecords::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户换股记录删除失败');
            }

            //16. 删除用户行权记录
            $ok = \app\common\model\EsopExerciseRecords::where('user_id', $userId)
                ->delete();
            if (!$ok) {
                throw new \Exception('用户行权记录删除失败');
            }
     
            Db::commit();
            return $this->jsonSucess('账号注销成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError('账号注销失败: ' . $e->getMessage());
        }
    }

    /**
     * 绑定证券账户
     * 
     * @ApiMethod (POST)
     * @ApiParams (name="securities_account", type="string", required=true, description="证券账户")
     * @return void
     */
    public function bindSecuritiesAccount()
    {
        // 获取当前登录用户ID
        $userId = $this->auth->id;
        if (!$userId) {
            return $this->toError('用户未登录或登录已过期');
        }
        
        // 获取证券账户参数
        $securitiesAccount = $this->request->post('securities_account');
        if (empty($securitiesAccount)) {
            return $this->toError('证券账户不能为空');
        }
        
        // 验证证券账户格式（这里可以根据实际需求添加格式验证）
        if (strlen($securitiesAccount) < 5 || strlen($securitiesAccount) > 50) {
            return $this->toError('证券账户格式不正确');
        }
        
        // 检查证券账户是否被其他用户使用
        $existProfile = EsopUserProfileModel::where('securities_account', $securitiesAccount)
            ->where('user_id', '<>', $userId)
            ->find();
        
        Db::startTrans();
        try {
            // 获取用户资料
            $userProfile = EsopUserProfileModel::getByUserId($userId);
            if (!$userProfile) {
                // 如果用户资料不存在，则创建
                $userProfile = new EsopUserProfileModel();
                $userProfile->user_id = $userId;
            }
            
            // 更新证券账户信息
            $userProfile->securities_account = $securitiesAccount;
            $userProfile->updated_at = date('Y-m-d H:i:s');
            $userProfile->save();
            
            Db::commit();
            return $this->jsonSucess('绑定证券账户成功');
        } catch (\Exception $e) {
            Db::rollback();
            return $this->toError('绑定证券账户失败：' . $e->getMessage());
        }
    }

    /**
     * 获取注销风险提示
     * 
     * @ApiMethod (GET)
     * @ApiReturn {"code":1,"msg":"获取注销风险提示成功","data":[]}
     */
    public function getDeleteRiskTips()
    {
        $tips = \app\common\model\EsopSystemConfig::where('key', 'delete_risk_tips')->value('value');
        return $this->jsonSucess('获取注销风险提示成功', ['tips' => $tips]);
    }   
} 