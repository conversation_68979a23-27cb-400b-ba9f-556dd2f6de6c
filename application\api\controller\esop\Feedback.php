<?php

namespace app\api\controller\esop;

use app\common\controller\Api;
use app\common\model\Feedback as FeedbackModel;
use think\Db;

/**
 * 意见反馈接口
 */
class Feedback extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = [];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];
    
    /**
     * 获取反馈列表
     *
     * @ApiTitle    (获取反馈列表)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/esop/feedback/list)
     * @ApiParams   (name="page", type="integer", required=true, description="页码")
     * @ApiParams   (name="limit", type="integer", required=true, description="每页数量")
     * @ApiParams   (name="status", type="integer", required=false, description="状态：0-待处理，1-已处理")
     * @ApiReturn   ({"code":1,"msg":"","time":1593749510,"data":{"total":1,"rows":[{"id":1,"user_id":1,"feedback_title":"测试反馈","feedback_content":"测试内容","status":0,"status_text":"待处理","created_at":"2021-01-01 00:00:00"}]}})
     */
    public function list()
    {
        $user = $this->auth->getUser();
        $page = $this->request->request('page', 1);
        $limit = $this->request->request('limit', 10);
        $status = $this->request->request('status');
        
        $where = ['user_id' => $user->id];
        if ($status !== '' && $status !== null) {
            $where['status'] = $status;
        }
        
        $list = FeedbackModel::where($where)
            ->order('id', 'desc')
            ->paginate($limit, false, ['page' => $page]);
            
        $result = [
            'total' => $list->total(),
            'rows' => $list->items()
        ];
        
        $this->success('', $result);
    }
    
    /**
     * 提交反馈
     *
     * @ApiTitle    (提交反馈)
     * @ApiMethod   (POST)
     * @ApiRoute    (/api/esop/feedback/submit)
     * @ApiParams   (name="feedback_title", type="string", required=true, description="反馈标题")
     * @ApiParams   (name="feedback_content", type="string", required=true, description="反馈内容")
     * @ApiParams   (name="feedback_images", type="string", required=false, description="反馈图片，多张图片用逗号分隔")
     * @ApiReturn   ({"code":1,"msg":"提交成功","time":1593749510,"data":[]})
     */
    public function submit()
    {
        $user = $this->auth->getUser();
        
        // 获取原始输入内容
        $params = $this->request->param(true);
        
        $feedback_title = isset($params['title']) ? $params['title'] : '';
        $feedback_content = isset($params['content']) ? $params['content'] : '';
        $feedback_images = isset($params['images']) ? $params['images'] : '';
        
        if (empty($feedback_title) || empty($feedback_content)) {
            $this->error('反馈标题和内容不能为空');
        }
        
        // 处理图片数组，转换为逗号分隔的字符串
        if (is_array($feedback_images)) {
            $feedback_images = implode(',', $feedback_images);
        }
        
        $data = [
            'user_id' => $user->id,
            'feedback_title' => $feedback_title,
            'feedback_content' => $feedback_content,
            'feedback_images' => $feedback_images,
            'status' => 0,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $result = FeedbackModel::create($data);
            if ($result) {
                $this->success('提交成功');
            } else {
                $this->error('提交失败');
            }
        } catch (\Exception $e) {
            throw $e;
            $this->error('提交失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取反馈详情
     *
     * @ApiTitle    (获取反馈详情)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/esop/feedback/detail)
     * @ApiParams   (name="id", type="integer", required=true, description="反馈ID")
     * @ApiReturn   ({"code":1,"msg":"","time":1593749510,"data":{"id":1,"user_id":1,"feedback_title":"测试反馈","feedback_content":"测试内容","feedback_images":"image1.jpg,image2.jpg","status":0,"status_text":"待处理","handle_result":"","created_at":"2021-01-01 00:00:00","updated_at":"2021-01-01 00:00:00"}})
     */
    public function detail()
    {
        $user = $this->auth->getUser();
        $id = $this->request->request('id');
        
        if (empty($id)) {
            $this->error('反馈ID不能为空');
        }
        
        $feedback = FeedbackModel::where(['id' => $id, 'user_id' => $user->id])->find();
        if (!$feedback) {
            $this->error('反馈不存在或无权查看');
        }
        
        // 处理图片字段为数组
        if (!empty($feedback['feedback_images'])) {
            $feedback['feedback_images_array'] = explode(',', $feedback['feedback_images']);
        } else {
            $feedback['feedback_images_array'] = [];
        }
        
        $this->success('', $feedback);
    }
} 