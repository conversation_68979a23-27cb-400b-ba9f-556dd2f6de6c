<?php

namespace app\admin\model\feedback;

use think\Model;
use app\common\model\User;

class FeedBack extends Model
{
    // 表名
    protected $name = 'esop_feedback';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'status_text'
    ];
    
    /**
     * 获取状态列表
     * @return array
     */
    public function getStatusList()
    {
        return ['0' => __('待处理'), '1' => __('已处理')];
    }
    
    /**
     * 获取状态文本
     * @param  string $value  值
     * @param  array  $data   行数据
     * @return string
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('app\common\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    /**
     * 关联管理员模型
     */
    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'handle_user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
} 