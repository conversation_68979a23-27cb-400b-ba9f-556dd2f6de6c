<?php

namespace app\common\model;

use think\Model;

/**
 * 轮播图模型
 */
class EsopCarouselImages extends Model
{
    // 表名
    protected $name = 'esop_carousel_images';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 追加属性
    protected $append = [
        'status_text',
    ];

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '禁用',
            1 => '启用',
        ];
        return isset($status[$data['status']]) ? $status[$data['status']] : '';
    }
} 