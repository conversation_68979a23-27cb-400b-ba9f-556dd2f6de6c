<?php

namespace app\admin\controller\transfer;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 转账管理
 *
 * @icon fa fa-exchange
 */
class Record extends Backend
{
    /**
     * Record模型对象
     * @var \app\admin\model\transfer\Record
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\transfer\Record;
        $this->view->assign("approvalStatusList", $this->model->getApprovalStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi/destroy方法
     * 因此在当前控制器中可不用编写增删改查的代码，除非需要自己控制这部分逻辑
     * 具体可参考FastAdmin手册
     */
    
    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with(['user', 'targetUser', 'userProfile', 'targetUserProfile'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['user', 'targetUser', 'userProfile', 'targetUserProfile'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 审批
     */
    public function approve($ids = null)
    {
        // 检查审批开关是否开启
        $systemConfig = \app\common\model\EsopSystemConfig::getConfig();
        if (empty($systemConfig['approve_switch']) || $systemConfig['approve_switch'] != 1) {
            $this->error('审批开关未打开');
        }

        $row = $this->model->with(['user', 'targetUser', 'userProfile', 'targetUserProfile'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 更新审批状态
                    $result = $row->save([
                        'approval_status' => $params['approval_status'],
                        'approval_remark' => $params['approval_remark'],
                        'approval_time' => date('Y-m-d H:i:s'),
                        'approval_user_id' => $this->auth->id
                    ]);
                    
                    // 如果审批通过，则更新用户资产
                    if ($params['approval_status'] == 1) {
                        // 获取转出用户的资产
                        $fromUserAsset = \app\common\model\EsopUserAsset::where('user_id', $row['user_id'])->find();
                        if (!$fromUserAsset || $fromUserAsset['locked_assets'] < $row['amount']) {
                            throw new \Exception('转出用户锁定资金不足');
                        }
                        
                        try {
                            // 使用changeUserAsset函数更新转出用户资产
                            \app\common\model\EsopUserAsset::changeUserAsset($row['user_id'], [
                                'locked_assets' => -$row['amount']
                            ]);
                            
                            // 使用changeUserAsset函数更新目标用户资产
                            \app\common\model\EsopUserAsset::changeUserAsset($row['target_user_id'], [
                                'available_assets' => $row['amount']
                            ]);
                            
                            // 重新获取更新后的资产信息
                            $updatedFromAsset = \app\common\model\EsopUserAsset::where('user_id', $row['user_id'])->find();
                            $updatedToAsset = \app\common\model\EsopUserAsset::where('user_id', $row['target_user_id'])->find();
                            
                            // 记录转出方资金变更
                            \app\common\model\EsopUserFundChangeRecords::addRecord(
                                $row['user_id'],
                                -$row['amount'],
                                \app\common\model\EsopUserFundChangeRecords::ACTION_TYPE_TRANSFER,
                                [
                                    'available_assets' => $fromUserAsset['available_assets'],
                                    'pending_assets' => $fromUserAsset['pending_assets'],
                                    'locked_assets' => $fromUserAsset['locked_assets']
                                ],
                                [
                                    'available_assets' => $updatedFromAsset['available_assets'],
                                    'pending_assets' => $updatedFromAsset['pending_assets'],
                                    'locked_assets' => $updatedFromAsset['locked_assets']
                                ],
                                '转出给用户ID:'.$row['target_user_id'],
                                ['user_id' => $row['user_id'], 'target_user_id' => $row['target_user_id']]
                            );
                            
                            // 记录接收方资金变更
                            $beforeToAsset = [
                                'available_assets' => $updatedToAsset ? ($updatedToAsset['available_assets'] - $row['amount']) : 0,
                                'pending_assets' => $updatedToAsset ? $updatedToAsset['pending_assets'] : 0,
                                'locked_assets' => $updatedToAsset ? $updatedToAsset['locked_assets'] : 0
                            ];
                            
                            \app\common\model\EsopUserFundChangeRecords::addRecord(
                                $row['target_user_id'],
                                $row['amount'],
                                \app\common\model\EsopUserFundChangeRecords::ACTION_TYPE_ACCEPT,
                                $beforeToAsset,
                                [
                                    'available_assets' => $updatedToAsset['available_assets'],
                                    'pending_assets' => $updatedToAsset['pending_assets'],
                                    'locked_assets' => $updatedToAsset['locked_assets']
                                ],
                                '来自用户ID:'.$row['user_id'].'的转入',
                                ['user_id' => $row['user_id'], 'target_user_id' => $row['target_user_id']]
                            );

                            // 记录资产变化日志
                           $logs = \app\common\model\EsopAssetChangeLog::getRowsByRelationId($row['id'], '转出');
                           foreach($logs as $log){
                                $log->audit_status = 1;
                                $log->save();
                           }

                        } catch (\Exception $e) {
                            throw new \Exception('资产更新失败：' . $e->getMessage());
                        }
                    }
                    // 如果审批拒绝，则解锁资金
                    else if ($params['approval_status'] == 2) {
                        // 获取转出用户的资产
                        $fromUserAsset = \app\common\model\EsopUserAsset::where('user_id', $row['user_id'])->find();
                        if (!$fromUserAsset) {
                            throw new \Exception('转出用户资产不存在');
                        }
                        
                        try {
                            // 使用changeUserAsset函数将锁定资金返还到可用资产
                            \app\common\model\EsopUserAsset::changeUserAsset($row['user_id'], [
                                'available_assets' => $row['amount'],
                                'locked_assets' => -$row['amount']
                            ]);
                            
                            // 重新获取更新后的资产信息
                            $updatedFromAsset = \app\common\model\EsopUserAsset::where('user_id', $row['user_id'])->find();
                            
                            // 记录资金变更
                            \app\common\model\EsopUserFundChangeRecords::addRecord(
                                $row['user_id'],
                                $row['amount'],
                                \app\common\model\EsopUserFundChangeRecords::ACTION_TYPE_UNLOCK_FUND,
                                [
                                    'available_assets' => $fromUserAsset['available_assets'],
                                    'pending_assets' => $fromUserAsset['pending_assets'],
                                    'locked_assets' => $fromUserAsset['locked_assets']
                                ],
                                [
                                    'available_assets' => $updatedFromAsset['available_assets'],
                                    'pending_assets' => $updatedFromAsset['pending_assets'],
                                    'locked_assets' => $updatedFromAsset['locked_assets']
                                ],
                                '转账申请被拒绝，锁定资金解锁返还',
                                ['user_id' => $row['user_id'], 'target_user_id' => $row['target_user_id']]
                            );

                            $logs = \app\common\model\EsopAssetChangeLog::getRowsByRelationId($row['id'], '转出');
                            foreach($logs as $log){
                                $log->audit_status = 2;
                                $log->save();
                            }
                        } catch (\Exception $e) {
                            throw new \Exception('资产更新失败：' . $e->getMessage());
                        }
                    }
                    
                    Db::commit();
                    return json_encode(['code' => 1, 'msg' => '审批成功']);
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
} 