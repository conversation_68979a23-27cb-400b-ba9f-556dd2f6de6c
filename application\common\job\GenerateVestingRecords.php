<?php

namespace app\common\job;

use think\queue\Job;
use think\Db;
use think\Exception;
use think\Log;
use think\Queue;
use think\console\Output;

/**
 * 生成解禁记录任务
 * 该任务用于生成解禁记录
 */
class GenerateVestingRecords
{
    /**
     * 执行任务
     * @param Job $job 队列任务
     * @param array $data 任务数据
     */
    public function fire(Job $job, $data)
    {
        try {
            // 记录开始执行任务
            Log::record("开始执行生成解禁记录任务: " . json_encode($data, JSON_UNESCAPED_UNICODE), 'info');

            // 检查数据完整性
            if (!isset($data['grant_id']) || !isset($data['rule_id'])) {
                Log::record("任务数据不完整，缺少grant_id或rule_id", 'error');
                $job->delete();
                return;
            }

            // 获取授权记录
            $grantId = $data['grant_id'];
            $ruleId = $data['rule_id'];

            $grant = Db::name('esop_grant_operation_logs')->where('id', $grantId)->find();
            $rule = Db::name('esop_vesting_rules')->where('id', $ruleId)->find();

            if (!$grant || !$rule) {
                $message = "未找到授权记录(ID:{$grantId})或规则(ID:{$ruleId})";
                Log::record($message, 'error');
                $job->delete();
                return;
            }

            Log::record("找到授权记录ID:{$grantId}和解禁规则ID:{$ruleId}", 'info');

            // 开始生成解禁记录，并插入解禁任务到队列
            $this->generateRecords($grant, $rule);

            // 任务完成，从队列中删除
            $job->delete();
            Log::record("任务完成，已从队列中删除", 'info');
        } catch (Exception $e) {
            // 任务失败次数+1
            $attempts = $job->attempts();

            // 记录错误信息
            Log::record("任务执行异常: " . $e->getMessage(), 'error');
            Log::record("异常文件: " . $e->getFile() . " 第" . $e->getLine() . "行", 'error');
            Log::record("堆栈跟踪: \n" . $e->getTraceAsString(), 'error');

            // 重试3次
            if ($attempts < 3) {
                // 延迟10分钟重试
                Log::record("这是第{$attempts}次尝试，10分钟后将重试", 'info');
                $job->release(600);
            } else {
                // 超过重试次数，记录失败日志并删除任务
                Log::record("已超过最大重试次数(3)，任务将被删除", 'error');

                Db::name('esop_system_logs')->insert([
                    'operation' => '生成解禁记录任务失败',
                    'ip_address' => '127.0.0.1',
                    'status' => 0,
                    'detail' => $e->getMessage(),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                $job->delete();
            }
        }
    }

    /**
     * 生成解禁记录
     * @param array $grant 授权记录
     * @param array $rule 解禁规则
     */
    private function generateRecords($grant, $rule)
    {
        try {
            // 开始事务
            Db::startTrans();
            Log::record("开始生成解禁记录事务...", 'info');

            $startDate = date('Y-m-d'); // 今天作为起始日期
            $totalAmount = $grant['grant_amount']; // 总解禁金额

            Log::record("解禁起始日期: {$startDate}, 总解禁金额: {$totalAmount}", 'info');

            // 根据规则类型计算当天解禁金额
            if ($rule['rule_type'] == 1) {
                // 按天数解禁
                $vestingDays = $rule['vesting_days'];
                if ($vestingDays <= 0) {
                    throw new \Exception('解禁天数配置错误');
                }

                // 记录解禁方式日志
                Log::record("按天数解禁: 总天数 {$vestingDays} 天, 是否递减: " . ($rule['is_decrease'] ? '是' : '否'), 'info');

                // 第一日无论递减与否，解禁金额计算方式一致
                // 递减模式和等额模式第一天都是总金额/总天数
                $amountToday = bcdiv($totalAmount, $vestingDays, 2);

                // 只一次解禁
                if ($vestingDays == 1) {
                    $amountToday = $totalAmount;
                }
                $pendingAmountAfter = bcsub($totalAmount, $amountToday, 2);

                Log::record("今日解禁金额: {$amountToday}, 剩余待解禁金额: {$pendingAmountAfter}", 'info');

            } elseif ($rule['rule_type'] == 2) {
                // 按比例解禁(递减)
                $percentage = $rule['vesting_percentage'];
                if ($percentage <= 0 || $percentage >= 100) {
                    throw new \Exception('解禁比例配置错误');
                }
                $ratio        = bcdiv($percentage, 100, 2); // 转换成小数
                $amountToday  = bcmul($totalAmount, $ratio, 2);

                // 若一次释放完毕, 直接全部释放
                if ($percentage == 100) {
                    $amountToday = $totalAmount;
                }
                $pendingAmountAfter = bcsub($totalAmount, $amountToday, 2);

                Log::record("按比例解禁: 比例 {$percentage}%, 今日解禁金额: {$amountToday}, 剩余待解禁金额: {$pendingAmountAfter}", 'info');
                
            }


            // 写入解禁记录
            Db::name('esop_vesting_records')->insert([
                'grant_id'       => $grant['id'],
                'rule_id'        => $rule['id'],
                'vesting_date'   => $startDate,
                'vesting_time'   => $rule['execution_time'],
                'vesting_amount' => $amountToday,
                'pending_amount' => $pendingAmountAfter,
                'vesting_status' => 0,
                'created_at'     => date('Y-m-d H:i:s'),
                'updated_at'     => date('Y-m-d H:i:s'),
                'target_user_id' => $grant['target_user_id']
            ]);
            
            // 获取新插入记录的ID
            $recordId = Db::name('esop_vesting_records')->getLastInsID();
            Log::record("解禁记录插入成功，ID: {$recordId}", 'info');

            // 提交事务
            Db::commit();
            Log::record("事务提交成功", 'info');

            // 插入解禁任务到队列
            $delay = $this->parseExecutionTimeAndDelay($rule['execution_time'], $startDate);
            Queue::later($delay['delay_seconds'], 'app\common\job\VestingExecutionJob', [
                'vesting_record_id' => $recordId,
            ],'vesting');

            Log::record("解禁任务插入队列成功", 'info');
            
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::record("事务回滚: " . $e->getMessage(), 'error');
        }
    }


    /**
     * 解析执行时间字符串并计算延迟秒数
     * @param string $executionTimeStr 执行时间字符串（如：1:30）
     * @param string $date 日期字符串（如：2024-06-01）
     * @return array 包含格式化的执行时间和延迟秒数的数组
     */
    private function parseExecutionTimeAndDelay($executionTimeStr, $date)
    {
        // 默认时间设为0:00
        $hour = 0;
        $minute = 0;

        // 解析时间字符串，支持"1:30"、"01:30"等格式
        if (preg_match('/^(\d{1,2}):(\d{1,2})$/', trim($executionTimeStr), $matches)) {
            $hour = (int)$matches[1];
            $minute = (int)$matches[2];
        }

        // 使用DateTime对象进行时间拼接和格式化
        try {
            // 创建目标日期时间对象
            $targetDateTime = new \DateTime($date);
            $targetDateTime->setTime($hour, $minute, 0);
            
            // 获取当前时间对象
            $currentDateTime = new \DateTime();
            
            // 使用diff计算时间差，返回DateInterval对象
            $interval = $currentDateTime->diff($targetDateTime);
            
            // 将DateInterval转换为秒数
            $seconds = $interval->days * 86400 + $interval->h * 3600 + $interval->i * 60 + $interval->s;
            
            // 如果目标时间早于当前时间，则时间差为负数
            if ($interval->invert) {
                $seconds = -$seconds;
            }
            
            // 如果目标时间已过，设置为立即执行(延迟1秒)
            $delay = max(1, $seconds);
            
            // 返回结果
            return [
                'execution_time' => $targetDateTime->format('Y-m-d H:i:s'),
                'delay_seconds' => $delay
            ];
        } catch (\Exception $e) {
            // 发生异常时，返回默认时间和延迟
            // 记录异常日志
            Log::record("解析执行时间异常: " . $e->getMessage(), 'error');
            
            return [
                'execution_time' => $date . ' 00:00:00',
                'delay_seconds' => 1
            ];
        }
    }
}
