<?php

namespace app\admin\controller\exercise;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 行权管理
 *
 * @icon fa fa-money
 */
class Record extends Backend
{
    /**
     * Record模型对象
     * @var \app\admin\model\exercise\Record
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\exercise\Record;
        $this->view->assign("approvalStatusList", $this->model->getApprovalStatusList());
        $this->view->assign("stockPriceTypeList", $this->model->getStockPriceTypeList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi/destroy方法
     * 因此在当前控制器中可不用编写增删改查的代码，除非需要自己控制这部分逻辑
     * 具体可参考FastAdmin手册
     */    
    
    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with(['user', 'stock', 'userProfile'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['user', 'stock', 'userProfile'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 审批
     */
    public function approve($ids = null)
    {
        $row = $this->model->with(['user', 'stock', 'userProfile'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                try {
                    // 开始事务
                    Db::startTrans();
                    
                    // 更新审批状态
                    $result = $row->save([
                        'approval_status' => $params['approval_status'],
                        'approval_remark' => $params['approval_remark'],
                        'approval_time' => date('Y-m-d H:i:s'),
                        'approval_user_id' => $this->auth->id
                    ]);
                    
                    // 如果审批拒绝，则归还股票
                    if ($params['approval_status'] == 2) {
                        // 使用changeUserStock方法归还股票
                        \app\common\model\EsopUserStockRecords::changeUserStock(
                            $row['user_id'],
                            $row['stock_id'],
                            $row['amount'],
                            '行权拒绝，股票归还'
                        );
                    }

                    // 记录资产变化日志
                    $log = \app\common\model\EsopAssetChangeLog::getRowByRelationId($row['id'], '行权');
                    if(!$log){
                        $log->audit_status = $params['approval_status'];
                        $log->save();
                    }
                    
                    Db::commit();
                    return json_encode(['code' => 1, 'msg' => '审批成功']);
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (\Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
} 