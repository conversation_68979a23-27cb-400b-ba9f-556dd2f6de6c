# Software Engineer Resume

## Personal Information
**Full Stack Developer | Backend Engineer**  
**Years of Experience:** 9+ years (2015-Present)  
**Specialization:** PHP, Golang, JavaScript, Full Stack Development

---

## Professional Summary
Experienced Full Stack Developer with 9+ years of expertise in backend development, API design, and system architecture. Proven track record in e-commerce platforms, financial systems, gaming applications, and cryptocurrency trading platforms. Strong background in microservices, distributed systems, and cross-platform integration.

---

## Technical Skills

### Programming Languages
- **Backend:** PHP, Golang, Python
- **Frontend:** JavaScript, Vue.js, HTML5, CSS3, SASS
- **Database:** MySQL, Redis
- **Mobile:** WeChat Mini Program Development

### Frameworks & Technologies
- **PHP:** <PERSON><PERSON>, CodeIgniter, Smarty
- **Golang:** Gin, Beego, Go-kit (Microservices)
- **Frontend:** Vue.js, Webpack, H5
- **Message Queue:** RabbitMQ
- **Search Engine:** Sphinx Full-text Search
- **Authentication:** JWT Token

### DevOps & Tools
- **Deployment:** Docker, Jenkins
- **Version Control:** Git
- **Task Scheduling:** Crontab
- **Cloud Services:** Alibaba Cloud SMS, Tencent IM
- **API Integration:** Telegram Bot API, Payment Gateways

---

## Work Experience

### Full Stack Engineer | Hubei Lianghu Xianda Network Technology Co., Ltd.
**Duration:** June 2022 - Present  
**Responsibilities:**
- Developed and maintained backend APIs for Lianghu Xianda agricultural e-commerce app
- Built and maintained admin panel frontend and backend systems
- Implemented new features based on operational requirements

**Technical Stack:**
- Backend API: Golang with Beego framework, JWT authentication
- Admin Panel: PHP with Laravel framework
- Frontend: Vue.js + SASS + Webpack
- Task Scheduling: Crontab
- Cloud Services: Alibaba Cloud SMS, Tencent IM

**Key Modules:**
- Payment system integration
- User management and authentication
- Order management system
- Role-based access control

### Golang Developer | Beijing Blue Star Technology Co., Ltd.
**Duration:** February 2022 - July 2022  
**Project:** China Construction Bank Security System

**Responsibilities:**
- Maintained and upgraded access control and daily management modules
- Developed gRPC services for emergency management module
- Implemented meeting control management (join, kick, terminal streaming, anonymous access)

**Technical Stack:**
- Web Layer: Gin framework + RabbitMQ
- Data Layer: Go-kit microservices toolkit

**Achievement:**
- Solved distributed WebSocket communication issues

### PHP Developer | Wuhan Shiyao Technology Co., Ltd.
**Duration:** June 2021 - February 2022  
**Project:** Cross-border E-commerce ERP System

**Responsibilities:**
- Developed and maintained ERP systems for eBay and Shopee platforms
- Integrated Walmart cross-border e-commerce platform
- Resolved legacy bugs and implemented new features
- Supported daily operational requirements

**Technical Stack:**
- Primary: Laravel framework
- Secondary: Golang with Gin framework
- Deployment: Jenkins + Docker

**Key Achievements:**
- Optimized large log file download performance and resolved interruption issues
- Successfully integrated Walmart platform into local ERP system

### PHP Developer | Beijing Jiayou Network Technology Co., Ltd.
**Duration:** May 2020 - June 2021  
**Project:** 7k7k Gaming Platform

**Responsibilities:**
- Collaborated with operations team for business requirement development
- Developed and maintained 7k7k website management backend
- Redesigned and maintained 7k7k main website
- Implemented payment system improvements
- Developed promotional activities and events

**Technical Stack:**
- Backend: CodeIgniter + MySQL + Redis
- Deployment: Jenkins
- Version Control: Git
- Automation: Golang for scheduled tasks (sitemap push, cache updates)

**Key Achievements:**
- Redesigned 7k7k main website
- Integrated third-party large-scale web games
- Developed lottery and promotional systems

### PHP Developer | Beijing Huashu Interactive Technology Co., Ltd.
**Duration:** September 2017 - May 2020  
**Responsibilities:**
- Developed APIs for mini-programs and H5 applications
- Implemented and maintained payment interfaces
- Managed WeChat official account development
- Led independent development of astrology project with algorithm implementation
- Maintained and updated legacy projects

**Technical Stack:**
- Backend: CodeIgniter + MySQL + Redis
- Search: Sphinx full-text search engine
- Template: Smarty
- Platform: WeChat Mini Program + Official Account

**Key Achievements:**
- WeChat Mini Program and Official Account management backend
- File caching system implementation
- Astrology business logic algorithms

### PHP Developer | Beijing Rongke Huitong Technology Co., Ltd.
**Duration:** March 2015 - May 2017  
**Project:** Health Commission Fund Management System

**Responsibilities:**
- Full responsibility for PHP project development
- Project requirement analysis and system design
- Code development and implementation
- Client communication and problem resolution
- Business logic and technical issue resolution

---

## Project Experience

### TG Bot Cryptocurrency Trading Platform | Full Stack Developer
**Duration:** December 2024 - Present  
**Description:** Cryptocurrency trading bot using Telegram Bot API for Solana token trading
**Key Features:**
- Buy/sell functionality
- Wallet management
- Referral commission system
- Multi-language support
- Gas fee configuration

### Skindeer CS:GO Skin Trading Platform | Full Stack Developer
**Duration:** September 2023 - Present  
**Description:** CS:GO skin trading platform with B2B and B2C functionality
**Features:**
- C-end: Web and mobile trading interface
- B-end: Integration with case opening websites and major skin merchants
- Multi-source inventory management (self-owned, partner platforms)

### 7k7k Payment System | PHP Backend Engineer
**Duration:** May 2020 - Present  
**Description:** Payment gateway for main site and third-party web games
**Features:**
- Platform and third-party game payments
- All mainstream payment methods support
- QR code integration
- Internal game currency recharge

### Astrology Applications | Research & Development
**Duration:** 2017 - Present  
**Projects:**
- **Chinese Calendar & Almanac:** Auspicious date calculation system
- **Soulmate Prediction:** Astrology-based compatibility analysis
- **English Name Generator:** Algorithm-based name suggestion system

---

## Education
**Hubei Polytechnic University**  
**Degree:** Associate Degree in Applied Electronic Technology  
**Duration:** 2012 - 2015

---

## Key Strengths
- **Full Stack Development:** Proficient in both frontend and backend technologies
- **System Architecture:** Experience with microservices and distributed systems
- **API Integration:** Extensive experience with third-party API integrations
- **Performance Optimization:** Proven track record in solving performance bottlenecks
- **Cross-Platform Development:** Web, mobile, and desktop application experience
- **Financial Systems:** Experience with payment gateways and trading platforms
- **Team Collaboration:** Strong communication skills with cross-functional teams
