<?php

namespace app\admin\controller\feedback;

use app\common\controller\Backend;
use app\common\model\Feedback as FeedbackModel;

/**
 * 意见反馈管理
 *
 * @icon fa fa-comment
 */
class Feedback extends Backend
{
    /**
     * Feedback模型对象
     * @var \app\common\model\Feedback
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new FeedbackModel;
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 查看
     */
    public function index()
    {
        // 设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            // 如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->with(['user', 'admin'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params['handle_user_id'] = $this->auth->id;
                $params['handle_time'] = date('Y-m-d H:i:s');
                
                $result = false;
                try {
                    // 更新反馈状态
                    $result = $row->allowField(true)->save($params);
                } catch (\think\exception\PDOException $e) {
                    $this->error($e->getMessage());
                } catch (\think\Exception $e) {
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No changes'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 处理图片字段
        $row['feedback_images'] = explode(',', $row['feedback_images']);
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
} 