<?php

namespace app\common\library;

class Stock
{
    /**
     * 获取指定股票代码的最新价格（通过同花顺接口，items[6]）
     * @param string $stockCode 股票代码，如HK0080
     * @return string|null 最新价格，获取失败返回null
     */
    public static function getLatestStockPrice($stockCode)
    {
        // 构建API接口URL，支持传入任意港股代码
        $url = "https://d.10jqka.com.cn/v6/realhead/hk_{$stockCode}/defer/last.js";
        // 使用curl方式获取接口返回内容，尽量模拟浏览器请求
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置超时时间为10秒
        // 设置常见浏览器User-Agent
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        // 设置常见的请求头，模拟浏览器
        $headers = [
            'Accept: application/json, text/javascript, */*; q=0.01',
            'Accept-Language: zh-CN,zh;q=0.9',
            'Connection: keep-alive',
            'Referer: https://www.10jqka.com.cn/', // 来源页
            'X-Requested-With: XMLHttpRequest',
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        // 跟随重定向
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        // 忽略SSL证书校验（如有https需求）
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        // 检查curl请求是否出错
        if ($response === false) {
            // 记录curl错误信息，便于排查
            // error_log('CURL Error: ' . curl_error($ch));
            curl_close($ch);
            return null;
        }
        curl_close($ch);
        // 提取JSON字符串（去除前缀和括号）
        $jsonStart = strpos($response, '(');
        $jsonEnd = strrpos($response, ')');
        if ($jsonStart === false || $jsonEnd === false) {
            // 格式不正确，返回null
            return null;
        }
        $jsonStr = substr($response, $jsonStart + 1, $jsonEnd - $jsonStart - 1);
        $data = json_decode($jsonStr, true);
        // 检查items和6号索引是否存在
        if (isset($data['items']) && isset($data['items']['10'])) {
            // 返回最新价格
            return $data['items']['10'];
        }
        // 未获取到价格，返回null
        return null;
    }

    /**
     * 获取日K线数据
     * @param string $stockCode 股票代码，如HK0080
     * @return array|null 日K线数据，获取失败返回null
     */
    public static function getDailyKLine($stockCode)
    {
        $url = "https://d.10jqka.com.cn/v6/line/hk_{$stockCode}/01/last.js";

        $headers = [
            'Accept: application/json, text/javascript, */*; q=0.01',
            'Accept-Language: zh-CN,zh;q=0.9',
            'Connection: keep-alive',
            'Referer: https://www.10jqka.com.cn/', // 来源页
            'X-Requested-With: XMLHttpRequest',
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        if ($response === false) {
            curl_close($ch);
            return null;
        }
        curl_close($ch);
        $jsonStart = strpos($response, '(');
        $jsonEnd = strrpos($response, ')');
        if ($jsonStart === false || $jsonEnd === false) {
            return null;
        }
        $jsonStr = substr($response, $jsonStart + 1, $jsonEnd - $jsonStart - 1);
        $data = json_decode($jsonStr, true);
        if (isset($data['data'])) {
            return $data;
        }
        return null;
    }

    /**
     * 计算指定股票n天的平均价格
     * @param string $stockCode 股票代码，如HK0080
     * @param int $days 天数，默认5天，最大120天
     * @param string $priceType 价格类型：open(开盘价), high(最高价), low(最低价), close(收盘价，默认)
     * @return float|null 平均价格，获取失败返回null
     */
    public static function getAveragePrice($stockCode, $days = 5, $priceType = 'close')
    {
        // 验证天数参数
        if ($days <= 0) {
            return null;
        }

        // 限制最大天数为120天（约4个月的交易日）
        $maxDays = self::getMaxAverageDays();
        if ($days > $maxDays) {
            $days = $maxDays;
        }

        // 获取K线数据
        $klineData = self::getDailyKLine($stockCode);
        if (!$klineData || !isset($klineData['data'])) {
            return null;
        }

        // 解析数据字符串
        $dataString = $klineData['data'];
        $dailyRecords = explode(';', trim($dataString));

        // 过滤空记录
        $dailyRecords = array_filter($dailyRecords, function($record) {
            return !empty(trim($record));
        });

        // 检查是否有足够的数据
        if (count($dailyRecords) < $days) {
            return null;
        }

        // 获取最近n天的数据（数组末尾是最新的数据）
        $recentRecords = array_slice($dailyRecords, -$days);

        $prices = [];
        $priceIndex = self::getPriceIndex($priceType);

        foreach ($recentRecords as $record) {
            $fields = explode(',', $record);
            if (count($fields) >= 5) {
                $price = $fields[$priceIndex];
                if ($price > 0) { // 过滤无效价格
                    $prices[] = $price;
                }
            }
        }

        // 计算平均价格
        if (empty($prices)) {
            return null;
        }

        return round(array_sum($prices) / count($prices), 4);
    }

    /**
     * 获取最大平均天数限制
     * @return int 最大天数
     */
    public static function getMaxAverageDays()
    {
        return 120; // 约4个月的交易日
    }

    /**
     * 获取指定股票的可用数据天数
     * @param string $stockCode 股票代码
     * @return int|null 可用天数，获取失败返回null
     */
    public static function getAvailableDataDays($stockCode)
    {
        // 获取K线数据
        $klineData = self::getDailyKLine($stockCode);
        if (!$klineData || !isset($klineData['data'])) {
            return null;
        }

        // 解析数据字符串
        $dataString = $klineData['data'];
        $dailyRecords = explode(';', trim($dataString));

        // 过滤空记录
        $dailyRecords = array_filter($dailyRecords, function($record) {
            return !empty(trim($record));
        });

        return count($dailyRecords);
    }

    /**
     * 获取价格类型对应的数组索引
     * @param string $priceType 价格类型
     * @return int 数组索引
     */
    private static function getPriceIndex($priceType)
    {
        $priceMap = [
            'open' => 1,   // 开盘价
            'high' => 2,   // 最高价
            'low' => 3,    // 最低价
            'close' => 4   // 收盘价
        ];

        return isset($priceMap[$priceType]) ? $priceMap[$priceType] : $priceMap['close'];
    }

    /**
     * 获取指定股票的详细K线数据（包含日期、价格等信息）
     * @param string $stockCode 股票代码，如HK0080
     * @param int $days 获取最近几天的数据，默认10天
     * @return array|null 详细K线数据数组，获取失败返回null
     */
    public static function getDetailedKLineData($stockCode, $days = 10)
    {
        // 获取K线数据
        $klineData = self::getDailyKLine($stockCode);
        if (!$klineData || !isset($klineData['data'])) {
            return null;
        }

        // 解析数据字符串
        $dataString = $klineData['data'];
        $dailyRecords = explode(';', trim($dataString));

        // 过滤空记录
        $dailyRecords = array_filter($dailyRecords, function($record) {
            return !empty(trim($record));
        });

        // 获取最近n天的数据
        $recentRecords = array_slice($dailyRecords, -$days);

        $result = [];
        foreach ($recentRecords as $record) {
            $fields = explode(',', $record);
            if (count($fields) >= 8) {
                $result[] = [
                    'date' => $fields[0],
                    'open' => floatval($fields[1]),
                    'high' => floatval($fields[2]),
                    'low' => floatval($fields[3]),
                    'close' => floatval($fields[4]),
                    'volume' => intval($fields[5]),
                    'amount' => floatval($fields[6]),
                    'turnover_rate' => floatval($fields[7])
                ];
            }
        }

        return $result;
    }
}