<form id="approve-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="c-exercise-id" class="control-label col-xs-12 col-sm-3">{:__('Exercise ID')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-exercise-id" value="{$row.id}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-user" class="control-label col-xs-12 col-sm-3">{:__('User')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-user" value="{$row.user_profile.real_name} {$row.user.phone|default=''}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-stock" class="control-label col-xs-12 col-sm-3">{:__('Stock')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-stock" value="{$row.stock.stock_code|default=''} {$row.stock.stock_name|default=''}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-amount" class="control-label col-xs-12 col-sm-3">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-amount" value="{$row.amount}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-stock-price" class="control-label col-xs-12 col-sm-3">{:__('Stock price')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-stock-price" value="{$row.stock_price}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-stock-price-type" class="control-label col-xs-12 col-sm-3">{:__('Stock price type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-stock-price-type" value="{:__($row.stock_price_type_text)}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-exercise-fee" class="control-label col-xs-12 col-sm-3">{:__('Exercise fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-exercise-fee" value="{$row.exercise_fee}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-exercise-amount" class="control-label col-xs-12 col-sm-3">{:__('Exercise amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="c-exercise-amount" value="{$row.exercise_amount}" disabled />
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                {foreach name="approvalStatusList" item="vo" key="k"}
                {if in_array($k, [1,2])} 
                <label for="row[approval_status]-{$k}"><input id="row[approval_status]-{$k}" name="row[approval_status]" type="radio" value="{$k}" {in name="k" value="1"}checked{/in} /> {$vo}</label>
                {/if}
                {/foreach}
            </div>
        </div>
    </div>
    
    <div class="form-group">
        <label for="c-approval-remark" class="control-label col-xs-12 col-sm-3">{:__('Approval remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-approval-remark" name="row[approval_remark]" class="form-control" rows="5"></textarea>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-3"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form> 