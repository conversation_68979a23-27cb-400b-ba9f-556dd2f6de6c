define(['jquery','bootstrap','backend','table','form'],function($,undefined,Backend,Table,Form){
    var Controller={
        index:function(){
            Table.api.init({extend:{
                index_url:'help/article/index'+location.search,
                add_url:'help/article/add',
                edit_url:'help/article/edit',
                del_url:'help/article/del',
                multi_url:'help/article/multi',
                table:'esop_articles'
            }});
            var table=$("#table");
            table.bootstrapTable({
                url:$.fn.bootstrapTable.defaults.extend.index_url,
                pk:'id',
                sortName:'id',
                columns:[[
                    {checkbox:true},
                    {field:'id',title:'ID',sortable:true},
                    {field:'title',title:'标题',operate:'LIKE'},
                    {field:'category_name',title:'分类',operate:'LIKE'},
                    {field:'b_account_name',title:'B端',operate:false},
                    {field:'is_top_text',title:'置顶',operate:false},
                    {field:'status_text',title:'状态',searchList:{"1":"发布","0":"草稿"},formatter:Table.api.formatter.status},
                    {field:'created_at',title:'创建时间',operate:'RANGE',addclass:'datetimerange',formatter:Table.api.formatter.datetime},
                    {field:'operate',title:'操作',table:table,events:Table.api.events.operate,formatter:Table.api.formatter.operate}
                ]]
            });
            Table.api.bindevent(table);
        },
        add:function(){Controller.api.bindevent();},
        edit:function(){Controller.api.bindevent();},
        api:{
            bindevent:function(){
                Form.api.bindevent($('form[role=form]'));
            }
        }
    };return Controller;
}); 