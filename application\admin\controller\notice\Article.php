<?php

namespace app\admin\controller\notice;

use app\common\controller\Backend;
use app\common\model\EsopArticle;
use app\common\model\EsopArticleCategory;
use app\common\model\EsopNoticeApprovalRecord;

/**
 * 公告中心文章管理
 * @icon fa fa-bullhorn
 */
class Article extends Backend
{
    protected $model = null;
    protected $sort  = 'id';
    protected $order = 'DESC';
    protected $boardId = 1; // 公告中心

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new EsopArticle();

        // 分类下拉
        $cats = EsopArticleCategory::where('board_id', $this->boardId)->column('category_name','id');
        $this->view->assign('categoryList', $cats);
        $this->view->assign('statusList', [1=>'发布',0=>'草稿']);
        $this->view->assign('topList', [0=>'否',1=>'是']);
    }

    /** 列表 */
    public function index()
    {
        $this->request->filter(['strip_tags','trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where,$sort,$order,$offset,$limit) = $this->buildparams();
            $list = $this->model
                ->with(['category','baccount'])
                ->where($where)
                ->where('board_id', $this->boardId)
                ->order($sort,$order)
                ->paginate($limit);

            $rows = [];
            foreach ($list as $row) {
                // 获取审批状态
                $approvalStatus = EsopNoticeApprovalRecord::where('notice_id',$row->id)->order('id','desc')->value('status');
                if ($approvalStatus === null) { $approvalStatus = 0; }
                $approvalText = $approvalStatus === 1 ? '通过' : ($approvalStatus === 2 ? '驳回' : '待审批');

                $rows[] = [
                    'id'             => $row->id,
                    'title'          => $row->title,
                    'category_name'  => $row->category ? $row->category->category_name : '',
                    'b_account_name' => $row->baccount ? $row->baccount->account_name : '通用',
                    'approval_status'      => $approvalStatus,
                    'approval_status_text' => $approvalText,
                    'is_top_text'    => $row->is_top ? '是' : '否',
                    'created_at'     => $row->created_at,
                ];
            }
            return json(['total'=>$list->total(),'rows'=>$rows]);
        }
        return $this->view->fetch();
    }

    /** 添加 */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a', [], 'trim');
            $params['board_id']  = $this->boardId;
            $params['author_id'] = $this->auth->id;
            $this->request->post(['row' => $params]);
        }
        return parent::add();
    }

    /**
     * 审批公告
     * @param int|null $ids 公告ID
     */
    public function approve($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('记录未找到'));
        }

        // 获取最新审批记录（若不存在则创建一个待审批记录，方便显示）
        $approval = EsopNoticeApprovalRecord::where('notice_id', $ids)->order('id', 'DESC')->find();
        if (!$approval) {
            $approval = new EsopNoticeApprovalRecord([ 'notice_id' => $ids, 'user_id' => $row->author_id, 'status' => 0 ]);
        }

        if ($this->request->isPost()) {
            $params = $this->request->post();
            if (!isset($params['status'])) {
                $this->error(__('审批结果不能为空'));
            }
            $status = intval($params['status']); // 1=通过 2=驳回
            $remark = $params['remark'] ?? '';
            if (!in_array($status, [1, 2])) {
                $this->error(__('无效的审批状态'));
            }

            \think\Db::startTrans();
            try {
                // 更新或创建审批记录
                $approval->status      = $status;
                $approval->remark      = $remark;
                $approval->updated_at  = date('Y-m-d H:i:s');
                $approval->save();

                \think\Db::commit();
            } catch (\Exception $e) {
                \think\Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success();
        }

        $this->view->assign('row', $row);
        $this->view->assign('approval', $approval);
        return $this->view->fetch();
    }
}