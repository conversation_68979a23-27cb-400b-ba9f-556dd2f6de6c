<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('标题')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required" class="form-control" name="row[title]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('分类')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-category_id" class="form-control selectpicker" name="row[category_id]" data-rule="required">
                {foreach name="categoryList" item="cname" key="cid"}
                <option value="{$cid}">{$cname}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('封面图片')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-cover_image" class="form-control" name="row[cover_image]" type="text">
                <span class="input-group-btn">
                    <button type="button" id="faupload-cover" class="btn btn-danger faupload" data-input-id="c-cover_image" data-mimetype="image/*" data-preview-id="p-cover"><i class="fa fa-upload"></i> {:__('Upload')}</button>
                    <button type="button" id="fachoose-cover" class="btn btn-primary fachoose" data-input-id="c-cover_image" data-mimetype="image/*"><i class="fa fa-list"></i> {:__('Choose')}</button>
                </span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-cover"></ul>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('摘要')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-summary" class="form-control" name="row[summary]" rows="3"></textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('内容')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" name="row[content]" rows="10" data-rule="required"></textarea>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', $statusList, 1)}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('置顶')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[is_top]', $topList, 0)}
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('排序值')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort_order" class="form-control" name="row[sort_order]" type="number" value="100">
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>