<?php

namespace app\common\model;

use think\Model;
use think\Db;
use think\Exception;

/**
 * 用户资产模型
 */
class EsopUserAsset extends Model
{
    // 表名
    protected $name = 'esop_user_assets';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';

    // 开启乐观锁
    protected $optimLock = 'version';

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo('EsopUser', 'user_id', 'id')->setEagerlyType(0);
    }
   
    
    /**
     * 增加或减少用户资产（使用条件更新确保资产安全）
     *
     * @param int $userId 用户ID
     * @param array $changes 变更数据，正数增加，负数减少
     * @return bool 操作结果
     * @throws Exception 更新失败异常
     */
    public static function changeUserAsset($userId, $changes)
    {
        Db::startTrans();
        try {
            // 查询用户资产记录
            $userAsset = self::where('user_id', $userId)->find();
            
            if (!$userAsset) {
                // 如果不存在则创建新记录
                $assetData = ['user_id' => $userId, 'version' => 1];
                foreach ($changes as $field => $value) {
                    $assetData[$field] = max(0, $value); // 确保新创建的记录字段值不小于0
                }
                
                // 设置时间字段
                $assetData['created_at'] = date('Y-m-d H:i:s');
                $assetData['updated_at'] = date('Y-m-d H:i:s');
                
                $result = (new self())->save($assetData);
                if (!$result) {
                    throw new Exception('创建用户资产记录失败');
                }
            } else {
                // 存在则使用条件更新
                foreach ($changes as $field => $value) {
                    if (isset($userAsset[$field])) {
                        // 对减少操作使用条件更新
                        if ($value < 0) {
                            $absValue = abs($value);
                            
                            // 使用条件更新确保余额充足
                            $affectedRows = Db::name('esop_user_assets')
                                ->where('user_id', $userId)
                                ->where($field, '>=', $absValue) // 确保余额充足
                                ->update([
                                    // 使用减法表达式，避免'+-'语法错误
                                    $field => Db::raw('GREATEST(0, ' . $field . ' - ' . $absValue . ')'), // 确保不小于0
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'version' => Db::raw('version+1') // 更新版本号
                                ]);
                            
                            if ($affectedRows == 0) {
                                throw new Exception($field . '资产不足');
                            }
                        } else {
                            // 增加操作可以直接更新
                            Db::name('esop_user_assets')
                                ->where('user_id', $userId)
                                ->update([
                                    $field => Db::raw($field . '+' . $value),
                                    'updated_at' => date('Y-m-d H:i:s'),
                                    'version' => Db::raw('version+1') // 更新版本号
                                ]);
                        }
                    }
                }
            }
            
            Db::commit();
            return true;
        } catch (Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
} 