<?php

namespace app\common\model;

use think\Model;

/**
 * 股票管理模型
 */
class EsopStockManagement extends Model
{
    // 表名
    protected $name = 'esop_stock_management';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 追加属性
    protected $append = [
        'price_type_text',
        'is_on_shelf_text'
    ];
    
    // 定义价格类型获取器
    public function getPriceTypeTextAttr($value, $data)
    {
        $priceTypes = [
            1 => __('Average price'),
            2 => __('Agreement price')
        ];
        return isset($priceTypes[$data['price_type']]) ? $priceTypes[$data['price_type']] : '';
    }
    
    // 定义上架状态获取器
    public function getIsOnShelfTextAttr($value, $data)
    {
        $status = [
            0 => __('Off shelf'),
            1 => __('On shelf')
        ];
        return isset($status[$data['is_on_shelf']]) ? $status[$data['is_on_shelf']] : '';
    }
    
    // 定义价格类型列表
    public static function getPriceTypeList()
    {
        return [
            1 => __('Average price'),
            2 => __('Agreement price')
        ];
    }
    
    // 定义上架状态列表
    public static function getIsOnShelfList()
    {
        return [
            0 => __('Off shelf'),
            1 => __('On shelf')
        ];
    }
} 