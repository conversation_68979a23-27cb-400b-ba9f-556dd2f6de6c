define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'baccount/account/index' + location.search,
                    add_url: 'baccount/account/add',
                    edit_url: 'baccount/account/edit',
                    // del_url: 'baccount/account/del', // 删除功能已移除
                    assets_url: 'baccount/account/assets',
                    // disable_url: 'baccount/account/disable', // 停用功能已移除
                    // enable_url: 'baccount/account/enable', // 启用功能已移除
                    multi_url: 'baccount/account/multi',
                    table: 'esop_b_accounts',
                }
            });

            var table = $("#table");
            
            // 资产管理按钮
            var assetsBtn = {
                name: 'assets',
                text: __('资产管理'),
                title: __('资产管理'),
                classname: 'btn btn-xs btn-info btn-dialog',
                icon: 'fa fa-money',
                url: 'baccount/account/assets',
                callback: function (data) {
                    $(".btn-refresh").trigger("click");
                }
            };

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('序号'), sortable: true},
                        {field: 'account_name', title: __('B端名称'), operate: 'LIKE'},
                        {field: 'alias', title: __('授权资产别名'), operate: 'LIKE'},
                        {field: 'admin_display', title: __('B端管理员'), operate: false},
                        {field: 'available_assets', title: __('可授权资产'), operate: false, formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return isNaN(num) ? value : num.toFixed(3);
                        }},
                        {field: 'invested_assets', title: __('已授权资产'), operate: false, formatter: function(value, row, index) {
                            var num = parseFloat(value);
                            return isNaN(num) ? value : num.toFixed(3);
                        }},
                        {field: 'team_member_count', title: __('团队会员数'), operate: false},
                        {field: 'is_audit', title: __('授权平台审核'), searchList: {"0":__('否'),"1":__('是')}, formatter: Table.api.formatter.normal},
                        {field: 'created_at', title: __('添加日期'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('操作'), table: table, events: Table.api.events.operate, buttons: [assetsBtn], formatter: function (value, row, index) {
                            var table = this.table;
                            // 默认按钮组
                            var buttons = $.extend([], this.buttons || []);
                            // 启用/停用按钮已移除
                            return Table.api.formatter.operate.call(this, value, row, index, {buttons: buttons});
                        }}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        assets: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});