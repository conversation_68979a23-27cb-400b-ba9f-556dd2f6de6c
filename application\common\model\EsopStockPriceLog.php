<?php

namespace app\common\model;

use think\Model;

/**
 * 股票价格设置记录模型
 */
class EsopStockPriceLog extends Model
{
    // 表名
    protected $name = 'esop_stock_price_logs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;
    protected $createTime = 'created_at';
    protected $updateTime = false;

    /**
     * 获取设置人信息
     */
    public function admin()
    {
        return $this->belongsTo('app\admin\model\Admin', 'set_by', 'id')->setEagerlyType(0);
    }
    
    /**
     * 获取关联的股票信息
     */
    public function stock()
    {
        return $this->belongsTo('app\common\model\EsopStockManagement', 'stock_id', 'id')->setEagerlyType(0);
    }
    
    /**
     * 价格类型获取器
     */
    public function getPriceTypeTextAttr($value, $data)
    {
        $priceTypes = [
            1 => __('Average price'),
            2 => __('Agreement price')
        ];
        return isset($priceTypes[$data['price_type']]) ? $priceTypes[$data['price_type']] : '';
    }
} 